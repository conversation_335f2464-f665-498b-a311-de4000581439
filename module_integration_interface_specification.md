# 模块间集成接口规格文档

## 1. 概述

模块间集成接口系统是体素游戏编辑器的核心架构组件，负责协调9个主要模块之间的数据交换、功能调用和资源共享。采用重构后的六大类架构设计，以体素编辑器为核心，建立统一的数据格式和智能的依赖管理系统。基于Electron + React + TypeScript技术栈，提供高性能的ARPG体素编辑器解决方案。

## 2. 重构后的模块架构

### 2.1 六大类模块架构
```typescript
interface NewEditorArchitecture {
  // === 1. 体素编辑器 (核心3D创作) ===
  voxelEditor: {
    coreFeatures: {
      modelCreation: boolean               // 模型创建
      animationEditor: boolean             // 内置动画编辑器 (窗口模式)
      terrainEditor: boolean               // 地形编辑
      buildingEditor: boolean              // 建筑编辑
      decorationEditor: boolean            // 装饰物编辑
      effectEditor: boolean                // 特效编辑 (体素特效)
    }
    
    // 内置动画编辑器窗口
    animationWindow: {
      windowMode: 'embedded' | 'floating'  // 嵌入式或浮动窗口
      realTimePreview: boolean             // 实时预览
      timelineEditor: boolean              // 时间轴编辑器
      keyframeEditor: boolean              // 关键帧编辑器
      boneSystem: boolean                  // 骨骼系统
    }
    
    // 模型生成系统
    modelGeneration: {
      unitModels: boolean                  // 单位模型
      itemModels: boolean                  // 物品模型
      buildingModels: boolean              // 建筑模型
      terrainModels: boolean               // 地形模型
      decorationModels: boolean            // 装饰物模型
      effectModels: boolean                // 特效模型
    }
  }
  
  // === 2. 数据编辑器 (游戏数据配置) ===
  dataEditor: {
    subEditors: {
      itemEditor: boolean                  // 物品编辑器
      unitEditor: boolean                  // 单位编辑器
      skillEditor: boolean                 // 技能编辑器
      effectEditor: boolean                // 特效编辑器 (数据配置)
      decorationEditor: boolean            // 装饰物编辑器
      terrainEditor: boolean               // 地形编辑器
      lifeSystemEditor: boolean            // 生活系统编辑器 (可选)
    }
    
    // 统一数据管理
    dataManagement: {
      unifiedDataFormat: 'JSON'            // 统一JSON格式
      schemaValidation: boolean            // 数据结构验证
      versionControl: boolean              // 版本控制
      dataSync: boolean                    // 数据同步
    }
  }
  
  // === 3. UI编辑器 (界面设计) ===
  uiEditor: {
    features: {
      interfaceDesign: boolean             // 界面设计
      hudEditor: boolean                   // HUD编辑器
      menuEditor: boolean                  // 菜单编辑器
      dialogEditor: boolean                // 对话框编辑器
      inventoryUIEditor: boolean           // 背包界面编辑器
    }
  }
  
  // === 4. 事件触发器 (游戏逻辑) ===
  eventTrigger: {
    features: {
      gameEventEditor: boolean             // 游戏事件编辑器
      triggerConditions: boolean           // 触发条件
      actionSequences: boolean             // 动作序列
      scriptIntegration: boolean           // 脚本集成
    }
  }
  
  // === 5. 资产库系统 (统一资源管理) ===
  assetLibrary: {
    libraries: {
      modelLibrary: boolean                // 模型库
      scriptLibrary: boolean               // 脚本库
      soundLibrary: boolean                // 声音库
      uiLibrary: boolean                   // UI库
      animationLibrary: boolean            // 动画库
      effectLibrary: boolean               // 特效库
    }
  }
  
  // === 6. 系统管理 (程序控制) ===
  systemManagement: {
    features: {
      programSettings: boolean             // 程序设置
      gameSystemSwitches: boolean          // 游戏功能开关
      performanceSettings: boolean         // 性能设置
      exportSettings: boolean              // 导出设置
    }
  }
}
```

### 2.2 核心集成关系
```typescript
interface CoreIntegrationRelationships {
  // === 体素编辑器作为核心 ===
  voxelEditorIntegration: {
    // 为所有数据编辑器提供模型
    modelProvision: {
      toItemEditor: {
        itemModels: string[]               // 物品模型ID列表
        itemAnimations: string[]           // 物品动画ID列表
        autoIconGeneration: boolean        // 自动图标生成
      }
      
      toUnitEditor: {
        unitModels: string[]               // 单位模型ID列表
        unitAnimations: string[]           // 单位动画ID列表
        skeletonData: string[]             // 骨骼数据
      }
      
      toDecorationEditor: {
        decorationModels: string[]         // 装饰物模型
        placementData: string[]            // 放置数据
      }
      
      toTerrainEditor: {
        terrainTiles: string[]             // 地形瓦片
        terrainTextures: string[]          // 地形纹理
      }
      
      toEffectEditor: {
        effectModels: string[]             // 特效模型
        particleData: string[]             // 粒子数据
      }
    }
    
    // 内置动画编辑器窗口
    embeddedAnimationEditor: {
      windowIntegration: {
        openTrigger: 'model_selected'      // 选中模型时打开
        windowPosition: 'right_panel'      // 右侧面板
        realTimeSync: boolean              // 实时同步
      }
      
      animationFeatures: {
        keyframeEditing: boolean           // 关键帧编辑
        boneAnimation: boolean             // 骨骼动画
        morphAnimation: boolean            // 变形动画
        particleAnimation: boolean         // 粒子动画
      }
    }
  }
  
  // === 数据编辑器间的集成 ===
  dataEditorIntegration: {
    // 技能编辑器为核心数据提供者
    skillEditorProvision: {
      toItemEditor: {
        equipmentSkills: string[]          // 装备技能ID
        itemSkills: string[]               // 道具技能ID
        skillValidation: boolean           // 技能验证
      }
      
      toUnitEditor: {
        unitSkills: string[]               // 单位技能ID
        skillTrees: string[]               // 技能树ID
        passiveSkills: string[]            // 被动技能ID
      }
      
      toLifeSystemEditor: {
        lifeSkillEnhancements: string[]    // 生活技能增强
        passiveGatheringSkills: string[]   // 被动采集技能
        passiveSurvivalSkills: string[]    // 被动生存技能
      }
    }
    
    // 统一脚本挂载系统 (类似Unity)
    scriptMountingSystem: {
      scriptTypes: {
        itemScripts: '##物品脚本##'
        unitScripts: '##单位脚本##'
        skillScripts: '##技能脚本##'
        effectScripts: '##特效脚本##'
        lifeSystemScripts: '##生活系统脚本##'
        globalScripts: '##全局脚本##'
      }
      
      mountingMechanism: {
        dragAndDrop: boolean               // 拖拽挂载
        scriptSelector: boolean            // 脚本选择器
        presetScripts: boolean             // 预设脚本
        customScripts: boolean             // 自定义脚本
      }
    }
  }
}
```

## 3. 技术栈集成接口

### 3.1 前端架构集成
```typescript
interface TechStackIntegration {
  // === 前端架构 ===
  frontendArchitecture: {
    framework: 'Electron + React + TypeScript'
    
    // Electron主进程
    mainProcess: {
      windowManagement: boolean            // 窗口管理
      fileSystemAccess: boolean           // 文件系统访问
      nativeIntegration: boolean           // 原生集成
    }
    
    // React渲染进程
    renderProcess: {
      componentArchitecture: {
        voxelEditorComponent: boolean      // 体素编辑器组件
        dataEditorComponents: boolean      // 数据编辑器组件
        uiEditorComponent: boolean         // UI编辑器组件
        assetLibraryComponent: boolean     // 资产库组件
      }
      
      stateManagement: {
        reduxToolkit: boolean              // Redux Toolkit状态管理
        globalState: boolean               // 全局状态
        moduleStates: boolean              // 模块状态
        persistentState: boolean           // 持久化状态
      }
    }
  }
  
  // === 3D引擎集成 ===
  threejsIntegration: {
    voxelRendering: {
      instancedMeshes: boolean             // 实例化网格
      voxelOptimization: boolean           // 体素优化
      lodSystem: boolean                   // LOD系统
    }
    
    animationSystem: {
      skeletalAnimation: boolean           // 骨骼动画
      morphTargets: boolean                // 变形目标
      animationMixer: boolean              // 动画混合器
    }
    
    effectsSystem: {
      particleSystem: boolean              // 粒子系统
      shaderEffects: boolean               // 着色器特效
      postProcessing: boolean              // 后处理
    }
  }
  
  // === 物理引擎集成 ===
  physicsIntegration: {
    engine: 'Cannon.js' | 'Ammo.js'
    
    features: {
      collisionDetection: boolean          // 碰撞检测
      rigidBodyPhysics: boolean            // 刚体物理
      particlePhysics: boolean             // 粒子物理
    }
  }
  
  // === UI组件集成 ===
  uiComponentIntegration: {
    library: 'Ant Design' | 'Material-UI'
    
    customComponents: {
      voxelCanvas: boolean                 // 体素画布
      timelineEditor: boolean              // 时间轴编辑器
      propertyPanel: boolean               // 属性面板
      assetBrowser: boolean                // 资产浏览器
    }
  }
  
  // === 脚本引擎集成 ===
  scriptEngineIntegration: {
    engine: 'JavaScript'
    
    features: {
      scriptExecution: boolean             // 脚本执行
      apiBinding: boolean                  // API绑定
      debugSupport: boolean                // 调试支持
      hotReload: boolean                   // 热重载
    }
  }
}
```

### 3.2 统一数据交换格式
```typescript
interface UnifiedDataFormat {
  // === JSON数据格式标准 ===
  jsonDataStandard: {
    // 通用数据结构
    commonStructure: {
      metadata: {
        id: string                         // 全局唯一ID
        name: string                       // 名称
        type: string                       // 类型
        version: string                    // 版本
        createdAt: Date                    // 创建时间
        modifiedAt: Date                   // 修改时间
        author: string                     // 作者
        tags: string[]                     // 标签
      }

      dependencies: {
        models: string[]                   // 依赖的模型ID
        animations: string[]               // 依赖的动画ID
        scripts: string[]                  // 依赖的脚本ID
        effects: string[]                  // 依赖的特效ID
        sounds: string[]                   // 依赖的音效ID
      }

      properties: {
        [key: string]: any                 // 自定义属性
      }
    }

    // 资源引用格式
    resourceReference: {
      referenceType: 'id' | 'path' | 'url'
      referenceValue: string
      fallbackValue?: string               // 备用值
      validationHash?: string              // 验证哈希
    }
  }

  // === 数据验证Schema ===
  validationSchema: {
    jsonSchema: boolean                    // JSON Schema验证
    customValidators: boolean              // 自定义验证器
    crossReferenceValidation: boolean      // 交叉引用验证
    realTimeValidation: boolean            // 实时验证
  }
}
```

## 4. 地形装饰物自动生成系统

### 4.1 200种装饰物预设分类
```typescript
interface TerrainDecorationPresets {
  // === 自然地形装饰物 (120种) ===
  naturalTerrains: {
    // 森林地形 (30种)
    forestTerrains: {
      temperateForest: {
        decorations: [
          // 树木 (10种)
          { id: 'oak_tree_young', name: '幼橡树', rarity: 'common', size: [4, 8, 4] }
          { id: 'oak_tree_mature', name: '成熟橡树', rarity: 'common', size: [8, 16, 8] }
          { id: 'oak_tree_ancient', name: '古橡树', rarity: 'uncommon', size: [12, 24, 12] }
          { id: 'birch_tree_slim', name: '细桦树', rarity: 'common', size: [3, 12, 3] }
          { id: 'birch_tree_cluster', name: '桦树丛', rarity: 'common', size: [6, 14, 6] }
          { id: 'maple_tree_red', name: '红枫树', rarity: 'uncommon', size: [6, 14, 6] }
          { id: 'maple_tree_golden', name: '金枫树', rarity: 'rare', size: [8, 16, 8] }
          { id: 'dead_oak', name: '枯橡树', rarity: 'uncommon', size: [6, 12, 6] }
          { id: 'hollow_tree', name: '空心树', rarity: 'rare', size: [8, 18, 8] }
          { id: 'twisted_tree', name: '扭曲树', rarity: 'rare', size: [6, 14, 6] }

          // 灌木丛 (8种)
          { id: 'berry_bush_red', name: '红浆果丛', rarity: 'common', size: [2, 2, 2] }
          { id: 'berry_bush_blue', name: '蓝浆果丛', rarity: 'common', size: [2, 2, 2] }
          { id: 'fern_patch', name: '蕨类丛', rarity: 'common', size: [3, 2, 3] }
          { id: 'thorny_bush', name: '荆棘丛', rarity: 'common', size: [2, 3, 2] }
          { id: 'flower_bush_white', name: '白花丛', rarity: 'common', size: [2, 2, 2] }
          { id: 'flower_bush_purple', name: '紫花丛', rarity: 'uncommon', size: [2, 2, 2] }
          { id: 'moss_covered_log', name: '苔藓原木', rarity: 'common', size: [6, 2, 2] }
          { id: 'mushroom_circle', name: '蘑菇圈', rarity: 'uncommon', size: [4, 1, 4] }

          // 地面装饰 (8种)
          { id: 'forest_grass_patch', name: '森林草丛', rarity: 'common', size: [2, 1, 2] }
          { id: 'wildflower_mix', name: '野花', rarity: 'common', size: [1, 1, 1] }
          { id: 'fallen_leaves', name: '落叶堆', rarity: 'common', size: [3, 1, 3] }
          { id: 'forest_stones', name: '森林石块', rarity: 'common', size: [2, 1, 2] }
          { id: 'tree_stump', name: '树桩', rarity: 'common', size: [3, 2, 3] }
          { id: 'forest_spring', name: '森林泉水', rarity: 'rare', size: [4, 1, 4] }
          { id: 'animal_burrow', name: '动物洞穴', rarity: 'uncommon', size: [2, 1, 2] }
          { id: 'fairy_ring', name: '仙女环', rarity: 'rare', size: [6, 1, 6] }

          // 特殊装饰 (4种)
          { id: 'forest_shrine', name: '森林神龛', rarity: 'rare', size: [4, 6, 4] }
          { id: 'druid_stone', name: '德鲁伊石', rarity: 'rare', size: [2, 4, 2] }
          { id: 'ancient_rune_stone', name: '古代符文石', rarity: 'epic', size: [3, 5, 3] }
          { id: 'forest_altar', name: '森林祭坛', rarity: 'epic', size: [6, 4, 6] }
        ]
        compatibleTerrains: ['grass', 'dirt', 'forest_floor']
        incompatibleElements: ['lava', 'ice', 'desert_sand', 'deep_water']
      }

      coniferousForest: {
        decorations: [
          // 针叶树 (8种)
          { id: 'pine_tree_small', name: '小松树', rarity: 'common', size: [4, 10, 4] }
          { id: 'pine_tree_large', name: '大松树', rarity: 'common', size: [6, 20, 6] }
          { id: 'spruce_tree_dense', name: '密云杉', rarity: 'common', size: [5, 16, 5] }
          { id: 'spruce_tree_tall', name: '高云杉', rarity: 'uncommon', size: [6, 24, 6] }
          { id: 'fir_tree_silver', name: '银冷杉', rarity: 'uncommon', size: [5, 18, 5] }
          { id: 'cedar_tree', name: '雪松', rarity: 'uncommon', size: [8, 20, 8] }
          { id: 'dead_pine', name: '枯松树', rarity: 'common', size: [4, 12, 4] }
          { id: 'lightning_struck_tree', name: '雷击树', rarity: 'rare', size: [6, 15, 6] }

          // 针叶林灌木 (6种)
          { id: 'juniper_bush', name: '杜松丛', rarity: 'common', size: [2, 3, 2] }
          { id: 'pine_sapling', name: '松树苗', rarity: 'common', size: [1, 2, 1] }
          { id: 'moss_boulder', name: '苔藓巨石', rarity: 'common', size: [4, 3, 4] }
          { id: 'lichen_rock', name: '地衣岩石', rarity: 'common', size: [3, 2, 3] }
          { id: 'pine_cone_pile', name: '松果堆', rarity: 'common', size: [2, 1, 2] }
          { id: 'resin_tree', name: '树脂树', rarity: 'uncommon', size: [5, 14, 5] }

          // 地面装饰 (4种)
          { id: 'pine_needle_carpet', name: '松针地毯', rarity: 'common', size: [4, 1, 4] }
          { id: 'forest_mushroom', name: '森林蘑菇', rarity: 'common', size: [1, 1, 1] }
          { id: 'granite_outcrop', name: '花岗岩露头', rarity: 'uncommon', size: [6, 4, 6] }
          { id: 'mountain_spring', name: '山泉', rarity: 'rare', size: [3, 1, 3] }
        ]
        compatibleTerrains: ['pine_needles', 'rocky_soil', 'mountain_grass']
        incompatibleElements: ['tropical_plants', 'desert_cacti', 'swamp_vegetation']
      }
    }

    // 草原地形 (25种)
    grasslandTerrains: {
      temperateGrassland: {
        decorations: [
          // 草类 (8种)
          { id: 'tall_prairie_grass', name: '高草原草', rarity: 'common', size: [2, 3, 2] }
          { id: 'short_meadow_grass', name: '短草甸草', rarity: 'common', size: [3, 1, 3] }
          { id: 'bunch_grass', name: '丛生草', rarity: 'common', size: [2, 2, 2] }
          { id: 'feather_grass', name: '羽毛草', rarity: 'common', size: [1, 2, 1] }
          { id: 'buffalo_grass', name: '野牛草', rarity: 'common', size: [4, 1, 4] }
          { id: 'blue_stem_grass', name: '蓝茎草', rarity: 'uncommon', size: [2, 4, 2] }
          { id: 'switch_grass', name: '柳枝稷', rarity: 'common', size: [3, 3, 3] }
          { id: 'wheat_grass', name: '小麦草', rarity: 'common', size: [2, 2, 2] }

          // 野花 (10种)
          { id: 'prairie_rose', name: '草原玫瑰', rarity: 'uncommon', size: [2, 2, 2] }
          { id: 'sunflower_wild', name: '野向日葵', rarity: 'uncommon', size: [2, 4, 2] }
          { id: 'black_eyed_susan', name: '黑心菊', rarity: 'common', size: [1, 2, 1] }
          { id: 'purple_coneflower', name: '紫锥花', rarity: 'common', size: [1, 2, 1] }
          { id: 'goldenrod', name: '一枝黄花', rarity: 'common', size: [1, 3, 1] }
          { id: 'prairie_aster', name: '草原紫菀', rarity: 'common', size: [1, 2, 1] }
          { id: 'indian_paintbrush', name: '火焰草', rarity: 'uncommon', size: [1, 2, 1] }
          { id: 'lupine_blue', name: '蓝羽扇豆', rarity: 'uncommon', size: [2, 3, 2] }
          { id: 'prairie_sage', name: '草原鼠尾草', rarity: 'common', size: [2, 2, 2] }
          { id: 'wildflower_meadow', name: '野花草甸', rarity: 'common', size: [4, 2, 4] }

          // 散布装饰 (7种)
          { id: 'prairie_boulder', name: '草原巨石', rarity: 'uncommon', size: [4, 3, 4] }
          { id: 'weathered_rock', name: '风化岩石', rarity: 'common', size: [2, 2, 2] }
          { id: 'prairie_dog_mound', name: '土拨鼠丘', rarity: 'common', size: [2, 1, 2] }
          { id: 'ant_hill', name: '蚁丘', rarity: 'common', size: [1, 1, 1] }
          { id: 'old_fence_post', name: '旧栅栏柱', rarity: 'uncommon', size: [1, 4, 1] }
          { id: 'prairie_pond', name: '草原池塘', rarity: 'rare', size: [6, 1, 6] }
          { id: 'buffalo_wallow', name: '野牛泥坑', rarity: 'uncommon', size: [4, 1, 4] }
        ]
        compatibleTerrains: ['grass', 'prairie_soil', 'meadow']
        incompatibleElements: ['dense_forest', 'mountain_peaks', 'swamp_water']
      }
    }
  }

  // === 人工地形装饰物 (80种) ===
  artificialTerrains: {
    // 古代遗迹 (25种)
    ancientRuins: {
      classicalRuins: {
        decorations: [
          // 建筑遗迹 (10种)
          { id: 'broken_column', name: '断裂石柱', rarity: 'common', size: [2, 8, 2] }
          { id: 'fallen_pillar', name: '倒塌柱子', rarity: 'common', size: [8, 2, 2] }
          { id: 'ruined_arch', name: '废墟拱门', rarity: 'uncommon', size: [8, 10, 4] }
          { id: 'cracked_statue', name: '破裂雕像', rarity: 'uncommon', size: [3, 6, 3] }
          { id: 'headless_statue', name: '无头雕像', rarity: 'uncommon', size: [3, 8, 3] }
          { id: 'ancient_altar', name: '古代祭坛', rarity: 'rare', size: [6, 4, 6] }
          { id: 'sacrificial_stone', name: '祭祀石', rarity: 'rare', size: [4, 3, 4] }
          { id: 'ruined_fountain', name: '废墟喷泉', rarity: 'rare', size: [6, 4, 6] }
          { id: 'collapsed_wall', name: '倒塌墙壁', rarity: 'common', size: [8, 4, 2] }
          { id: 'ancient_doorway', name: '古代门廊', rarity: 'uncommon', size: [4, 8, 2] }

          // 装饰元素 (8种)
          { id: 'carved_relief', name: '浮雕', rarity: 'uncommon', size: [4, 6, 1] }
          { id: 'stone_gargoyle', name: '石像鬼', rarity: 'rare', size: [2, 4, 2] }
          { id: 'ancient_urn', name: '古代瓮', rarity: 'uncommon', size: [2, 3, 2] }
          { id: 'ritual_bowl', name: '仪式碗', rarity: 'uncommon', size: [3, 2, 3] }
          { id: 'inscribed_tablet', name: '铭文石板', rarity: 'rare', size: [3, 4, 1] }
          { id: 'ancient_brazier', name: '古代火盆', rarity: 'uncommon', size: [2, 3, 2] }
          { id: 'weathered_sphinx', name: '风化狮身人面像', rarity: 'epic', size: [8, 6, 4] }
          { id: 'obelisk_fragment', name: '方尖碑碎片', rarity: 'rare', size: [2, 12, 2] }

          // 神秘元素 (7种)
          { id: 'glowing_rune_stone', name: '发光符文石', rarity: 'rare', size: [2, 4, 2] }
          { id: 'crystal_formation', name: '水晶阵', rarity: 'rare', size: [4, 3, 4] }
          { id: 'ancient_portal', name: '古代传送门', rarity: 'epic', size: [6, 8, 2] }
          { id: 'levitating_stone', name: '悬浮石', rarity: 'epic', size: [3, 3, 3] }
          { id: 'time_worn_idol', name: '时光雕像', rarity: 'rare', size: [2, 5, 2] }
          { id: 'mystic_circle', name: '神秘法阵', rarity: 'rare', size: [8, 1, 8] }
          { id: 'ancient_observatory', name: '古代观星台', rarity: 'epic', size: [10, 8, 10] }
        ]
        compatibleTerrains: ['stone_floor', 'ancient_ground', 'marble', 'weathered_stone']
        incompatibleElements: ['modern_furniture', 'household_items', 'industrial_equipment']
      }
    }

    // 村庄城镇 (20种)
    settlementAreas: {
      medievalVillage: {
        decorations: [
          // 建筑装饰 (8种)
          { id: 'wooden_fence', name: '木栅栏', rarity: 'common', size: [4, 2, 1] }
          { id: 'stone_well', name: '石井', rarity: 'uncommon', size: [3, 4, 3] }
          { id: 'market_stall', name: '市场摊位', rarity: 'common', size: [4, 3, 2] }
          { id: 'village_sign', name: '村庄路标', rarity: 'common', size: [1, 4, 1] }
          { id: 'hay_bale', name: '干草堆', rarity: 'common', size: [3, 2, 3] }
          { id: 'water_trough', name: '水槽', rarity: 'common', size: [4, 2, 2] }
          { id: 'hitching_post', name: '拴马桩', rarity: 'common', size: [1, 3, 1] }
          { id: 'village_shrine', name: '村庄神龛', rarity: 'uncommon', size: [3, 5, 3] }

          // 农业装饰 (6种)
          { id: 'vegetable_garden', name: '菜园', rarity: 'common', size: [6, 1, 6] }
          { id: 'wheat_field', name: '麦田', rarity: 'common', size: [8, 2, 8] }
          { id: 'scarecrow', name: '稻草人', rarity: 'common', size: [2, 4, 2] }
          { id: 'mill_wheel', name: '水车', rarity: 'uncommon', size: [6, 6, 2] }
          { id: 'grain_silo', name: '粮仓', rarity: 'uncommon', size: [4, 8, 4] }
          { id: 'chicken_coop', name: '鸡舍', rarity: 'common', size: [4, 3, 4] }

          // 生活用品 (6种)
          { id: 'wooden_cart', name: '木车', rarity: 'common', size: [6, 3, 3] }
          { id: 'barrel_stack', name: '木桶堆', rarity: 'common', size: [3, 4, 3] }
          { id: 'tool_rack', name: '工具架', rarity: 'common', size: [2, 4, 1] }
          { id: 'wash_basin', name: '洗脸盆', rarity: 'common', size: [2, 2, 2] }
          { id: 'cooking_pot', name: '大锅', rarity: 'common', size: [3, 2, 3] }
          { id: 'village_bell', name: '村钟', rarity: 'uncommon', size: [2, 6, 2] }
        ]
        compatibleTerrains: ['cobblestone', 'dirt_path', 'village_ground', 'wooden_floor']
        incompatibleElements: ['wild_animals', 'volcanic_elements', 'deep_forest_plants']
      }
    }

    // 宗教场所 (15种)
    religiousSites: {
      temples: {
        decorations: [
          // 神庙装饰 (8种)
          { id: 'temple_pillar', name: '神庙石柱', rarity: 'uncommon', size: [2, 12, 2] }
          { id: 'deity_statue', name: '神像', rarity: 'rare', size: [4, 10, 4] }
          { id: 'offering_altar', name: '供奉祭坛', rarity: 'uncommon', size: [6, 3, 4] }
          { id: 'incense_burner', name: '香炉', rarity: 'common', size: [2, 3, 2] }
          { id: 'prayer_wheel', name: '转经轮', rarity: 'uncommon', size: [2, 4, 2] }
          { id: 'temple_bell', name: '寺钟', rarity: 'uncommon', size: [3, 4, 3] }
          { id: 'sacred_pool', name: '圣池', rarity: 'rare', size: [8, 2, 8] }
          { id: 'temple_guardian', name: '守护神像', rarity: 'rare', size: [3, 8, 3] }

          // 宗教符号 (7种)
          { id: 'holy_symbol', name: '圣徽', rarity: 'uncommon', size: [2, 4, 1] }
          { id: 'prayer_flags', name: '经幡', rarity: 'common', size: [1, 6, 1] }
          { id: 'meditation_stone', name: '冥想石', rarity: 'uncommon', size: [2, 2, 2] }
          { id: 'sacred_tree', name: '圣树', rarity: 'rare', size: [6, 16, 6] }
          { id: 'ritual_circle', name: '仪式圆环', rarity: 'rare', size: [10, 1, 10] }
          { id: 'divine_fountain', name: '神圣喷泉', rarity: 'rare', size: [6, 4, 6] }
          { id: 'celestial_globe', name: '天球仪', rarity: 'epic', size: [4, 6, 4] }
        ]
        compatibleTerrains: ['marble_floor', 'temple_stone', 'sacred_ground']
        incompatibleElements: ['household_furniture', 'industrial_items', 'profane_objects']
      }
    }

    // 工业遗迹 (10种)
    industrialRuins: {
      decorations: [
        { id: 'rusted_machinery', name: '锈蚀机械', rarity: 'uncommon', size: [6, 4, 4] }
        { id: 'broken_conveyor', name: '破损传送带', rarity: 'common', size: [8, 2, 2] }
        { id: 'abandoned_furnace', name: '废弃熔炉', rarity: 'uncommon', size: [4, 6, 4] }
        { id: 'old_steam_pipe', name: '旧蒸汽管', rarity: 'common', size: [1, 8, 1] }
        { id: 'collapsed_chimney', name: '倒塌烟囱', rarity: 'uncommon', size: [3, 12, 3] }
        { id: 'rusted_tank', name: '锈蚀储罐', rarity: 'uncommon', size: [6, 8, 6] }
        { id: 'broken_crane', name: '破损起重机', rarity: 'rare', size: [8, 12, 6] }
        { id: 'abandoned_rail', name: '废弃铁轨', rarity: 'common', size: [8, 1, 2] }
        { id: 'old_mining_cart', name: '旧矿车', rarity: 'common', size: [4, 2, 2] }
        { id: 'industrial_debris', name: '工业废料', rarity: 'common', size: [4, 2, 4] }
      ]
      compatibleTerrains: ['concrete', 'industrial_floor', 'metal_grating']
      incompatibleElements: ['natural_vegetation', 'religious_symbols', 'pristine_nature']
    }

    // 道路交通 (10种)
    pathwaysAndRoads: {
      decorations: [
        { id: 'milestone_marker', name: '里程碑', rarity: 'common', size: [1, 3, 1] }
        { id: 'road_sign', name: '路标', rarity: 'common', size: [1, 4, 1] }
        { id: 'stone_bridge', name: '石桥', rarity: 'uncommon', size: [12, 4, 4] }
        { id: 'wooden_bridge', name: '木桥', rarity: 'common', size: [10, 2, 4] }
        { id: 'guard_tower', name: '哨塔', rarity: 'uncommon', size: [4, 12, 4] }
        { id: 'toll_booth', name: '收费亭', rarity: 'uncommon', size: [3, 4, 3] }
        { id: 'waypoint_shrine', name: '路标神龛', rarity: 'uncommon', size: [2, 4, 2] }
        { id: 'rest_area', name: '休息区', rarity: 'common', size: [6, 2, 6] }
        { id: 'caravan_stop', name: '商队驿站', rarity: 'uncommon', size: [8, 4, 8] }
        { id: 'crossroads_marker', name: '十字路口标记', rarity: 'common', size: [2, 3, 2] }
      ]
      compatibleTerrains: ['stone_road', 'dirt_path', 'cobblestone']
      incompatibleElements: ['dense_wilderness', 'untouched_nature', 'sacred_groves']
    }
  }
}
```

### 4.2 智能适配规则系统
```typescript
interface IntelligentCompatibilitySystem {
  // === 地形适配规则引擎 ===
  compatibilityRules: {
    // 环境逻辑规则
    environmentalLogic: {
      // 气候兼容性
      climateCompatibility: {
        temperature: {
          tropical: { compatible: ['palm_trees', 'tropical_flowers'], incompatible: ['pine_trees', 'snow_elements'] }
          temperate: { compatible: ['oak_trees', 'wildflowers'], incompatible: ['cacti', 'ice_formations'] }
          arctic: { compatible: ['arctic_plants', 'ice_elements'], incompatible: ['desert_plants', 'tropical_vegetation'] }
        }

        moisture: {
          arid: { compatible: ['cacti', 'desert_rocks'], incompatible: ['water_plants', 'moss'] }
          humid: { compatible: ['ferns', 'moss'], incompatible: ['desert_cacti', 'dry_grasses'] }
          wet: { compatible: ['water_lilies', 'cattails'], incompatible: ['desert_elements', 'fire_elements'] }
        }
      }

      // 生态系统规则
      ecosystemRules: {
        forestEcosystem: {
          canCoexist: ['trees', 'undergrowth', 'forest_animals', 'mushrooms']
          conflicts: ['desert_plants', 'arctic_elements', 'deep_water_plants']
          synergies: [
            { primary: 'large_trees', secondary: 'shade_plants', effect: 'enhanced_growth' }
            { primary: 'dead_trees', secondary: 'mushrooms', effect: 'decomposer_boost' }
          ]
        }

        desertEcosystem: {
          canCoexist: ['cacti', 'desert_shrubs', 'sand_formations', 'desert_animals']
          conflicts: ['water_plants', 'dense_vegetation', 'ice_elements']
          synergies: [
            { primary: 'large_cacti', secondary: 'small_desert_plants', effect: 'shade_protection' }
            { primary: 'oasis', secondary: 'palm_trees', effect: 'water_dependency' }
          ]
        }

        aquaticEcosystem: {
          canCoexist: ['water_plants', 'amphibious_elements', 'water_stones']
          conflicts: ['fire_elements', 'desert_plants', 'dry_terrain_elements']
          synergies: [
            { primary: 'water_source', secondary: 'water_plants', effect: 'nutrient_flow' }
            { primary: 'shallow_water', secondary: 'cattails', effect: 'edge_habitat' }
          ]
        }
      }
    }

    // 文化逻辑规则
    culturalLogic: {
      // 建筑风格兼容性
      architecturalStyle: {
        ancient: {
          compatible: ['stone_structures', 'weathered_elements', 'mystical_symbols']
          incompatible: ['modern_furniture', 'industrial_equipment', 'contemporary_art']
          contextual: [
            { item: 'deity_statue', requires: ['temple_context', 'sacred_ground'] }
            { item: 'ancient_altar', forbids: ['household_items', 'mundane_objects'] }
          ]
        }

        medieval: {
          compatible: ['wooden_structures', 'stone_buildings', 'agricultural_tools']
          incompatible: ['futuristic_elements', 'high_tech_items', 'modern_appliances']
          contextual: [
            { item: 'village_well', requires: ['settlement_area', 'community_space'] }
            { item: 'market_stall', forbids: ['wilderness_setting', 'sacred_spaces'] }
          ]
        }

        religious: {
          compatible: ['sacred_symbols', 'ceremonial_objects', 'spiritual_elements']
          incompatible: ['profane_objects', 'commercial_items', 'industrial_equipment']
          contextual: [
            { item: 'temple_statue', requires: ['consecrated_ground', 'worship_space'] }
            { item: 'offering_altar', forbids: ['domestic_furniture', 'secular_decorations'] }
          ]
        }
      }

      // 功能逻辑规则
      functionalLogic: {
        domesticSpaces: {
          compatible: ['furniture', 'household_items', 'comfort_objects']
          incompatible: ['wild_animals', 'dangerous_elements', 'sacred_artifacts']
          contextual: [
            { item: 'cooking_pot', requires: ['kitchen_area', 'domestic_setting'] }
            { item: 'wash_basin', forbids: ['outdoor_wilderness', 'sacred_temples'] }
          ]
        }

        wildernessAreas: {
          compatible: ['natural_elements', 'wild_plants', 'geological_features']
          incompatible: ['domestic_furniture', 'urban_infrastructure', 'indoor_items']
          contextual: [
            { item: 'animal_burrow', requires: ['natural_terrain', 'undisturbed_area'] }
            { item: 'wild_berry_bush', forbids: ['paved_areas', 'indoor_spaces'] }
          ]
        }

        sacredSpaces: {
          compatible: ['religious_symbols', 'ceremonial_objects', 'spiritual_decorations']
          incompatible: ['mundane_objects', 'commercial_items', 'profane_decorations']
          contextual: [
            { item: 'holy_relic', requires: ['consecrated_area', 'worship_context'] }
            { item: 'prayer_altar', forbids: ['commercial_spaces', 'domestic_areas'] }
          ]
        }
      }
    }

    // 物理逻辑规则
    physicalLogic: {
      // 重力和支撑
      structuralSupport: {
        requiresSupport: ['hanging_objects', 'suspended_elements', 'climbing_plants']
        providesSupport: ['trees', 'pillars', 'rock_formations', 'buildings']
        floatingElements: ['magical_objects', 'levitating_stones', 'spirit_orbs']
      }

      // 水源依赖
      waterDependency: {
        requiresWater: ['water_plants', 'amphibious_creatures', 'water_features']
        avoidsWater: ['fire_elements', 'desert_plants', 'electrical_objects']
        waterSources: ['springs', 'rivers', 'ponds', 'wells']
      }

      // 光照需求
      lightRequirements: {
        needsSunlight: ['most_plants', 'solar_elements', 'light_dependent_crystals']
        prefersShade: ['mushrooms', 'cave_plants', 'nocturnal_elements']
        lightSources: ['sun', 'magical_crystals', 'fire_elements', 'bioluminescent_plants']
      }
    }
  }

  // === 自动验证系统 ===
  autoValidationSystem: {
    // 实时兼容性检查
    realTimeValidation: {
      onPlacement: {
        checkEnvironmentalFit: boolean     // 检查环境适配
        checkCulturalContext: boolean      // 检查文化背景
        checkPhysicalLogic: boolean        // 检查物理逻辑
        showWarnings: boolean              // 显示警告
        suggestAlternatives: boolean       // 建议替代方案
      }

      conflictResolution: {
        autoRemoveConflicts: boolean       // 自动移除冲突
        highlightProblems: boolean         // 高亮问题区域
        provideSolutions: boolean          // 提供解决方案
        allowOverride: boolean             // 允许用户覆盖
      }
    }

    // 智能建议系统
    intelligentSuggestions: {
      contextAwareSuggestions: {
        basedOnTerrain: boolean            // 基于地形的建议
        basedOnExisting: boolean           // 基于现有装饰的建议
        basedOnTheme: boolean              // 基于主题的建议
        basedOnDensity: boolean            // 基于密度的建议
      }

      complementaryElements: {
        suggestPairs: boolean              // 建议配对元素
        suggestGroups: boolean             // 建议群组元素
        suggestTransitions: boolean        // 建议过渡元素
        suggestFocalPoints: boolean        // 建议焦点元素
      }
    }
  }
}
```

## 5. 主界面资产管理系统

### 5.1 统一资产库界面
```typescript
interface MainInterfaceAssetManagement {
  // === 主界面布局 ===
  mainInterfaceLayout: {
    layout: {
      topMenuBar: boolean                  // 顶部菜单栏
      leftToolPanel: boolean               // 左侧工具面板
      centerWorkspace: boolean             // 中央工作区
      rightPropertyPanel: boolean          // 右侧属性面板
      bottomAssetLibrary: boolean          // 底部资产库
    }

    // 资产库面板
    assetLibraryPanel: {
      tabLayout: {
        modelLibraryTab: boolean           // 模型库选项卡
        scriptLibraryTab: boolean          // 脚本库选项卡
        soundLibraryTab: boolean           // 声音库选项卡
        uiLibraryTab: boolean              // UI库选项卡
        animationLibraryTab: boolean       // 动画库选项卡
        effectLibraryTab: boolean          // 特效库选项卡
      }

      searchAndFilter: {
        globalSearch: boolean              // 全局搜索
        categoryFilter: boolean            // 分类筛选
        tagFilter: boolean                 // 标签筛选
        dependencyFilter: boolean          // 依赖筛选
      }
    }
  }

  // === 依赖管理系统 ===
  dependencyManagement: {
    // 自动检查选项卡
    autoCheckTab: {
      realTimeDependencyCheck: boolean     // 实时依赖检查
      brokenLinkDetection: boolean         // 断链检测
      circularDependencyDetection: boolean // 循环依赖检测
      autoRepair: boolean                  // 自动修复

      notifications: {
        missingAssets: boolean             // 缺失资产通知
        versionConflicts: boolean          // 版本冲突通知
        performanceWarnings: boolean       // 性能警告
      }
    }

    // 可视化选项卡
    visualizationTab: {
      dependencyGraph: boolean             // 依赖关系图
      interactiveGraph: boolean            // 交互式图表
      hierarchicalView: boolean            // 层级视图
      impactAnalysis: boolean              // 影响分析

      graphFeatures: {
        nodeFiltering: boolean             // 节点筛选
        edgeHighlighting: boolean          // 边缘高亮
        clusterAnalysis: boolean           // 集群分析
        pathFinding: boolean               // 路径查找
      }
    }

    // 自动切换/切断机制
    autoSwitchingMechanism: {
      onResourceModification: {
        autoUpdateReferences: boolean      // 自动更新引用
        notifyDependents: boolean          // 通知依赖者
        versionBumping: boolean            // 版本递增
      }

      onResourceDeletion: {
        safeDeletion: boolean              // 安全删除
        dependencyWarning: boolean         // 依赖警告
        alternativeSuggestion: boolean     // 替代建议
      }
    }
  }
}
```

## 6. 性能优化策略

### 6.1 ARPG编辑器优化
```typescript
interface PerformanceOptimization {
  // === 快速ARPG编辑器优化 ===
  arpgOptimization: {
    // 实时编辑优化
    realTimeEditing: {
      incrementalUpdates: boolean          // 增量更新
      lazyLoading: boolean                 // 懒加载
      virtualScrolling: boolean            // 虚拟滚动
      debounceUpdates: boolean             // 防抖更新
    }

    // 3D渲染优化
    renderingOptimization: {
      frustumCulling: boolean              // 视锥剔除
      occlusionCulling: boolean            // 遮挡剔除
      lodSystem: boolean                   // LOD系统
      instancedRendering: boolean          // 实例化渲染
    }

    // 内存管理优化
    memoryOptimization: {
      objectPooling: boolean               // 对象池化
      textureAtlasing: boolean             // 纹理图集
      geometryMerging: boolean             // 几何体合并
      garbageCollection: boolean           // 垃圾回收优化
    }

    // 数据处理优化
    dataProcessingOptimization: {
      webWorkers: boolean                  // Web Workers
      streamProcessing: boolean            // 流处理
      cacheOptimization: boolean           // 缓存优化
      compressionOptimization: boolean     // 压缩优化
    }

    // 大规模装饰物优化
    massiveDecorationOptimization: {
      // 实例化渲染
      instancedRendering: {
        instancedMeshes: boolean           // 实例化网格
        instancedMaterials: boolean        // 实例化材质
        gpuInstancing: boolean             // GPU实例化
        maxInstancesPerType: number        // 每类型最大实例数
      }

      // LOD系统
      lodSystem: {
        distanceBasedLOD: boolean          // 基于距离的LOD
        screenSizeBasedLOD: boolean        // 基于屏幕尺寸的LOD
        lodLevels: number                  // LOD等级数
        lodTransitionSmoothing: boolean    // LOD过渡平滑
      }

      // 剔除优化
      cullingOptimization: {
        frustumCulling: boolean            // 视锥剔除
        occlusionCulling: boolean          // 遮挡剔除
        distanceCulling: boolean           // 距离剔除
        densityCulling: boolean            // 密度剔除
      }

      // 内存管理
      memoryManagement: {
        objectPooling: boolean             // 对象池化
        textureAtlasing: boolean           // 纹理图集
        geometryMerging: boolean           // 几何体合并
        streamingLoading: boolean          // 流式加载
      }
    }
  }
}
```

---

## 总结

模块间集成接口系统作为体素游戏编辑器的核心架构，提供了：

### 核心架构优势
- **重构后的六大类架构**: 体素编辑器、数据编辑器、UI编辑器、事件触发器、资产库、系统管理
- **体素编辑器为核心**: 内置动画编辑器，为所有模块提供3D模型支持
- **统一脚本挂载系统**: 类似Unity的脚本挂载机制，支持##类型##脚本分类
- **智能依赖管理**: 自动检查和可视化两种依赖管理模式

### 技术栈集成
- **现代前端架构**: Electron + React + TypeScript
- **高性能3D引擎**: Three.js + Cannon.js/Ammo.js
- **统一数据格式**: JSON格式的数据交换标准
- **智能验证系统**: 实时数据验证和兼容性检查

### 地形装饰物系统
- **200种预设装饰物**: 120种自然地形 + 80种人工地形装饰物
- **智能适配规则**: 环境逻辑、文化逻辑、物理逻辑的三重验证
- **自动生成算法**: 多层次生成算法，支持WE风格参数控制
- **实时验证**: 防止不合理组合（如泉水中的熔岩、家庭中的神像）

### 性能优化
- **ARPG优化**: 专门针对ARPG游戏的性能优化策略
- **大规模渲染**: 实例化渲染、LOD系统、智能剔除
- **内存管理**: 对象池化、纹理图集、流式加载
- **实时编辑**: 增量更新、懒加载、防抖机制

### 用户体验
- **无缝集成**: 编辑器间的无缝跳转和数据传递
- **智能建议**: 基于上下文的智能装饰物建议
- **可视化管理**: 依赖关系图和影响分析
- **自动化处理**: 自动更新引用、版本管理、冲突解决

这个集成接口系统确保了各模块间的高效协作，为创建强大而快速的ARPG体素编辑器提供了坚实的技术基础。
```
```
