# 体素编辑器物品生成系统规格文档

## 1. 概述

体素编辑器物品生成系统是与物品编辑器深度集成的专用模块，专门用于生成游戏中所有物品和装备的3D体素模型。采用智能批量生成技术，一次生成10个变体供用户选择，支持自动图标生成和手动精修功能。集成统一的游戏资产库系统，提供200个预设物品模板和50种物品技能预设。

## 2. 核心功能

### 2.1 智能批量生成系统
```typescript
interface ItemGenerationSystem {
  // 生成约束
  generationConstraints: {
    maxVoxelsPerItem: 300                  // 最大体素数量限制
    itemSizeLimits: {
      maxDimensions: [16, 16, 16]          // 最大尺寸 (小于世界方块32x32x32)
      minDimensions: [2, 2, 2]             // 最小尺寸
      recommendedRatios: {                 // 推荐比例
        weapons: [1, 4, 1]                 // 武器 (宽:长:厚)
        armor: [3, 3, 2]                   // 护甲 (宽:高:厚)
        accessories: [2, 2, 1]             // 饰品 (宽:高:厚)
        tools: [1, 3, 1]                   // 工具 (宽:长:厚)
        consumables: [2, 3, 2]             // 消耗品 (宽:高:厚)
        containers: [3, 3, 3]              // 容器 (宽:高:厚)
      }
    }
  }
  
  // 批量生成配置
  batchGeneration: {
    variantCount: 10                       // 固定生成10个变体
    generationModes: {
      templateBased: boolean               // 基于模板生成
      proceduralGeneration: boolean        // 程序化生成
      hybridMode: boolean                  // 混合模式
    }
    
    variationParameters: {
      shapeVariation: number               // 形状变化程度 (0-100%)
      detailVariation: number              // 细节变化程度 (0-100%)
      colorVariation: number               // 颜色变化程度 (0-100%)
      sizeVariation: number                // 尺寸变化程度 (0-20%)
      qualityScaling: boolean              // 品质缩放
    }
  }
  
  // 自动图标生成
  iconGeneration: {
    autoGenerate: true                     // 自动生成图标
    renderSettings: {
      cameraAngle: {
        zRotation: 45                      // Z轴倾斜45度
        xRotation: 0                       // X轴旋转
        yRotation: 0                       // Y轴旋转
      }
      lighting: 'standard'                 // 标准光照
      backgroundColor: 'transparent'       // 透明背景
      iconSize: [64, 64]                   // 图标尺寸
      antiAliasing: true                   // 抗锯齿
    }
  }

  // === 道具类模板 (100个) ===
  itemTemplates: {
    // 消耗品模板 (40个)
    consumables: {
      // 食物 (15个)
      food: [
        { id: 'bread_loaf', name: '面包', type: '食物', size: [4, 2, 6], complexity: 'low' }
        { id: 'meat_cooked', name: '熟肉', type: '食物', size: [3, 2, 4], complexity: 'low' }
        { id: 'fish_grilled', name: '烤鱼', type: '食物', size: [2, 1, 6], complexity: 'low' }
        { id: 'apple_red', name: '红苹果', type: '水果', size: [2, 2, 2], complexity: 'low' }
        { id: 'cheese_wheel', name: '奶酪', type: '食物', size: [3, 2, 3], complexity: 'low' }
        { id: 'soup_bowl', name: '汤', type: '食物', size: [3, 2, 3], complexity: 'medium' }
        { id: 'stew_pot', name: '炖菜', type: '食物', size: [4, 3, 4], complexity: 'medium' }
        { id: 'pie_fruit', name: '水果派', type: '食物', size: [4, 2, 4], complexity: 'medium' }
        { id: 'cake_slice', name: '蛋糕', type: '食物', size: [3, 3, 2], complexity: 'medium' }
        { id: 'honey_jar', name: '蜂蜜', type: '食物', size: [2, 3, 2], complexity: 'medium' }
        { id: 'wine_bottle', name: '酒', type: '饮品', size: [2, 6, 2], complexity: 'medium' }
        { id: 'water_flask', name: '水壶', type: '饮品', size: [3, 4, 3], complexity: 'medium' }
        { id: 'milk_jug', name: '牛奶', type: '饮品', size: [3, 5, 3], complexity: 'medium' }
        { id: 'tea_cup', name: '茶', type: '饮品', size: [2, 2, 2], complexity: 'low' }
        { id: 'coffee_mug', name: '咖啡', type: '饮品', size: [2, 3, 2], complexity: 'low' }
      ]

      // 药水 (15个)
      potions: [
        { id: 'potion_health', name: '生命药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_mana', name: '法力药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_stamina', name: '体力药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_strength', name: '力量药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_speed', name: '速度药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_invisibility', name: '隐身药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_fire_resist', name: '抗火药水', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_poison', name: '毒药', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'potion_antidote', name: '解毒剂', type: '药水', size: [2, 4, 2], complexity: 'medium' }
        { id: 'elixir_greater', name: '高级药剂', type: '药剂', size: [2, 5, 2], complexity: 'high' }
        { id: 'tonic_basic', name: '滋补剂', type: '药剂', size: [2, 3, 2], complexity: 'low' }
        { id: 'salve_healing', name: '治疗膏', type: '药膏', size: [3, 2, 3], complexity: 'medium' }
        { id: 'oil_weapon', name: '武器油', type: '药油', size: [2, 3, 2], complexity: 'medium' }
        { id: 'powder_magic', name: '魔法粉', type: '粉末', size: [3, 2, 3], complexity: 'medium' }
        { id: 'crystal_mana', name: '法力水晶', type: '水晶', size: [2, 3, 2], complexity: 'high' }
      ]

      // 种子 (10个)
      seeds: [
        { id: 'seed_wheat', name: '小麦种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'seed_carrot', name: '胡萝卜种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'seed_potato', name: '土豆种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'seed_corn', name: '玉米种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'seed_tomato', name: '番茄种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'seed_flower', name: '花种子', type: '种子', size: [1, 1, 1], complexity: 'low' }
        { id: 'sapling_oak', name: '橡树苗', type: '树苗', size: [2, 4, 2], complexity: 'medium' }
        { id: 'sapling_pine', name: '松树苗', type: '树苗', size: [2, 5, 2], complexity: 'medium' }
        { id: 'sapling_fruit', name: '果树苗', type: '树苗', size: [2, 3, 2], complexity: 'medium' }
        { id: 'bulb_magic', name: '魔法球茎', type: '种子', size: [2, 2, 2], complexity: 'high' }
      ]
    }

    // 功能道具模板 (30个)
    functional: {
      // 书籍 (10个)
      books: [
        { id: 'book_basic', name: '书籍', type: '书籍', size: [3, 4, 1], complexity: 'low' }
        { id: 'tome_large', name: '大部头', type: '书籍', size: [4, 6, 2], complexity: 'medium' }
        { id: 'grimoire_magic', name: '魔法书', type: '魔法书', size: [3, 5, 2], complexity: 'high' }
        { id: 'manual_craft', name: '制作手册', type: '手册', size: [3, 4, 1], complexity: 'medium' }
        { id: 'journal_leather', name: '皮革日志', type: '日志', size: [3, 4, 1], complexity: 'medium' }
        { id: 'scroll_parchment', name: '羊皮纸卷', type: '卷轴', size: [1, 6, 1], complexity: 'low' }
        { id: 'map_folded', name: '地图', type: '地图', size: [4, 1, 3], complexity: 'medium' }
        { id: 'recipe_card', name: '配方卡', type: '卡片', size: [2, 3, 1], complexity: 'low' }
        { id: 'letter_sealed', name: '密信', type: '信件', size: [2, 3, 1], complexity: 'low' }
        { id: 'contract_legal', name: '契约', type: '文件', size: [3, 4, 1], complexity: 'medium' }
      ]

      // 钥匙 (10个)
      keys: [
        { id: 'key_iron', name: '铁钥匙', type: '钥匙', size: [1, 4, 1], complexity: 'low' }
        { id: 'key_brass', name: '黄铜钥匙', type: '钥匙', size: [1, 4, 1], complexity: 'medium' }
        { id: 'key_silver', name: '银钥匙', type: '钥匙', size: [1, 4, 1], complexity: 'medium' }
        { id: 'key_gold', name: '金钥匙', type: '钥匙', size: [1, 4, 1], complexity: 'high' }
        { id: 'key_skeleton', name: '万能钥匙', type: '钥匙', size: [1, 5, 1], complexity: 'high' }
        { id: 'keycard_basic', name: '门卡', type: '通行证', size: [2, 3, 1], complexity: 'medium' }
        { id: 'token_access', name: '通行令牌', type: '令牌', size: [2, 2, 1], complexity: 'medium' }
        { id: 'badge_guard', name: '守卫徽章', type: '徽章', size: [2, 3, 1], complexity: 'medium' }
        { id: 'seal_royal', name: '王室印章', type: '印章', size: [2, 2, 2], complexity: 'high' }
        { id: 'crystal_key', name: '水晶钥匙', type: '钥匙', size: [1, 5, 1], complexity: 'high' }
      ]

      // 容器 (10个)
      containers: [
        { id: 'bag_leather', name: '皮背包', type: '背包', size: [6, 8, 4], complexity: 'medium' }
        { id: 'bag_cloth', name: '布袋', type: '袋子', size: [4, 6, 4], complexity: 'low' }
        { id: 'chest_wooden', name: '木箱', type: '箱子', size: [8, 6, 6], complexity: 'medium' }
        { id: 'chest_iron', name: '铁箱', type: '箱子', size: [8, 6, 6], complexity: 'high' }
        { id: 'pouch_coin', name: '钱袋', type: '小袋', size: [3, 4, 3], complexity: 'medium' }
        { id: 'pouch_herb', name: '药草袋', type: '小袋', size: [3, 4, 3], complexity: 'medium' }
        { id: 'toolbox_basic', name: '工具箱', type: '工具箱', size: [6, 4, 4], complexity: 'medium' }
        { id: 'case_weapon', name: '武器匣', type: '武器箱', size: [10, 4, 3], complexity: 'high' }
        { id: 'barrel_storage', name: '储物桶', type: '桶', size: [6, 8, 6], complexity: 'medium' }
        { id: 'sack_grain', name: '粮袋', type: '麻袋', size: [5, 8, 5], complexity: 'low' }
      ]
    }

    // 材料模板 (20个)
    materials: {
      // 基础材料 (10个)
      basic: [
        { id: 'wood_log', name: '原木', type: '木材', size: [2, 8, 2], complexity: 'low' }
        { id: 'wood_plank', name: '木板', type: '木材', size: [4, 1, 6], complexity: 'low' }
        { id: 'stone_block', name: '石块', type: '石头', size: [3, 3, 3], complexity: 'low' }
        { id: 'stone_brick', name: '石砖', type: '石头', size: [2, 2, 4], complexity: 'low' }
        { id: 'iron_ingot', name: '铁锭', type: '金属锭', size: [2, 1, 4], complexity: 'medium' }
        { id: 'steel_ingot', name: '钢锭', type: '金属锭', size: [2, 1, 4], complexity: 'medium' }
        { id: 'copper_ingot', name: '铜锭', type: '金属锭', size: [2, 1, 4], complexity: 'medium' }
        { id: 'cloth_bolt', name: '布匹', type: '布料', size: [4, 2, 4], complexity: 'low' }
        { id: 'leather_hide', name: '皮革', type: '皮革', size: [4, 1, 6], complexity: 'medium' }
        { id: 'rope_bundle', name: '绳索束', type: '绳索', size: [3, 3, 3], complexity: 'medium' }
      ]

      // 稀有材料 (10个)
      rare: [
        { id: 'gem_ruby', name: '红宝石', type: '宝石', size: [1, 2, 1], complexity: 'high' }
        { id: 'gem_sapphire', name: '蓝宝石', type: '宝石', size: [1, 2, 1], complexity: 'high' }
        { id: 'gem_emerald', name: '绿宝石', type: '宝石', size: [1, 2, 1], complexity: 'high' }
        { id: 'gem_diamond', name: '钻石', type: '宝石', size: [1, 2, 1], complexity: 'high' }
        { id: 'crystal_magic', name: '魔法水晶', type: '魔法水晶', size: [2, 4, 2], complexity: 'high' }
        { id: 'crystal_power', name: '能量水晶', type: '魔法水晶', size: [2, 3, 2], complexity: 'high' }
        { id: 'ore_mithril', name: '秘银矿', type: '稀有矿物', size: [3, 2, 3], complexity: 'high' }
        { id: 'ore_adamant', name: '精金矿', type: '稀有矿物', size: [3, 2, 3], complexity: 'high' }
        { id: 'scale_dragon', name: '龙鳞', type: '龙鳞', size: [2, 1, 3], complexity: 'high' }
        { id: 'feather_phoenix', name: '凤凰羽毛', type: '神兽材料', size: [1, 6, 1], complexity: 'high' }
      ]
    }

    // 特殊物品模板 (10个)
    special: {
      // 任务物品 (5个)
      quest: [
        { id: 'artifact_ancient', name: '古代神器', type: '任务物品', size: [3, 4, 2], complexity: 'high' }
        { id: 'relic_holy', name: '神圣遗物', type: '任务物品', size: [2, 5, 2], complexity: 'high' }
        { id: 'fragment_crystal', name: '水晶碎片', type: '收集品', size: [2, 2, 1], complexity: 'medium' }
        { id: 'evidence_crime', name: '犯罪证据', type: '证据', size: [2, 3, 1], complexity: 'medium' }
        { id: 'sample_research', name: '研究样本', type: '样本', size: [2, 2, 2], complexity: 'medium' }
      ]

      // 货币 (5个)
      currency: [
        { id: 'coin_gold', name: '金币', type: '金币', size: [1, 1, 1], complexity: 'medium' }
        { id: 'coin_silver', name: '银币', type: '银币', size: [1, 1, 1], complexity: 'low' }
        { id: 'coin_copper', name: '铜币', type: '铜币', size: [1, 1, 1], complexity: 'low' }
        { id: 'gem_currency', name: '宝石币', type: '宝石币', size: [1, 1, 1], complexity: 'high' }
        { id: 'token_honor', name: '荣誉点', type: '荣誉点', size: [2, 2, 1], complexity: 'medium' }
      ]
    }
  }
}
```

### 2.3 物品技能预设系统 (50种)
```typescript
interface ItemSkillPresets {
  // === 装备被动技能预设 (15种) ===
  equipmentPassiveSkills: [
    { id: 'strength_boost_5', name: '力量+5', description: '增加5点力量属性', bonusType: 'flat', value: 5 }
    { id: 'strength_boost_10', name: '力量+10', description: '增加10点力量属性', bonusType: 'flat', value: 10 }
    { id: 'strength_boost_15', name: '力量+15', description: '增加15点力量属性', bonusType: 'flat', value: 15 }
    { id: 'agility_boost_5', name: '敏捷+5', description: '增加5点敏捷属性', bonusType: 'flat', value: 5 }
    { id: 'agility_boost_10', name: '敏捷+10', description: '增加10点敏捷属性', bonusType: 'flat', value: 10 }
    { id: 'intelligence_boost_5', name: '智力+5', description: '增加5点智力属性', bonusType: 'flat', value: 5 }
    { id: 'intelligence_boost_10', name: '智力+10', description: '增加10点智力属性', bonusType: 'flat', value: 10 }
    { id: 'vitality_boost_5', name: '体力+5', description: '增加5点体力属性', bonusType: 'flat', value: 5 }
    { id: 'fire_resistance_25', name: '火焰抗性+25%', description: '增加25%火焰抗性', bonusType: 'percentage', value: 25 }
    { id: 'ice_resistance_25', name: '冰霜抗性+25%', description: '增加25%冰霜抗性', bonusType: 'percentage', value: 25 }
    { id: 'poison_resistance_50', name: '毒素抗性+50%', description: '增加50%毒素抗性', bonusType: 'percentage', value: 50 }
    { id: 'night_vision', name: '夜视', description: '在黑暗中能够清晰视物', duration: -1 }
    { id: 'water_breathing', name: '水下呼吸', description: '在水下无限呼吸', duration: -1 }
    { id: 'exp_bonus_20', name: '经验加成+20%', description: '获得经验增加20%', bonusType: 'percentage', value: 20 }
    { id: 'drop_rate_15', name: '掉落率+15%', description: '物品掉落率增加15%', bonusType: 'percentage', value: 15 }
  ]

  // === 装备主动技能预设 (10种) ===
  equipmentActiveSkills: [
    { id: 'lightning_chain', name: '闪电链', description: '释放弹射闪电攻击多个敌人', skillType: 'projectile', cooldown: 10, manaCost: 30 }
    { id: 'heal_self', name: '治疗术', description: '恢复自身生命值', skillType: 'direct_cast', cooldown: 15, manaCost: 25, healAmount: 100 }
    { id: 'teleport_short', name: '短距传送', description: '瞬间传送到指定位置', skillType: 'movement', cooldown: 20, manaCost: 40, range: 10 }
    { id: 'berserker_rage', name: '狂暴', description: '攻击力+100%，持续30秒', skillType: 'buff', cooldown: 60, duration: 30, bonusValue: 100 }
    { id: 'magic_shield', name: '魔法护盾', description: '吸收500点伤害', skillType: 'buff', cooldown: 45, shieldAmount: 500 }
    { id: 'speed_boost', name: '急速', description: '移动速度+50%，持续20秒', skillType: 'buff', cooldown: 30, duration: 20, bonusValue: 50 }
    { id: 'invisibility', name: '隐身术', description: '隐身10秒', skillType: 'buff', cooldown: 90, duration: 10 }
    { id: 'fireball', name: '火球术', description: '发射火球攻击敌人', skillType: 'projectile', cooldown: 5, manaCost: 20, damage: 80 }
    { id: 'summon_skeleton', name: '召唤骷髅', description: '召唤骷髅战士协助战斗', skillType: 'summon', cooldown: 120, duration: 60 }
    { id: 'fire_wall', name: '火墙术', description: '在地面创建火墙', skillType: 'placement', cooldown: 25, manaCost: 35, duration: 15 }
  ]

  // === 装备触发技能预设 (10种) ===
  equipmentTriggerSkills: [
    { id: 'lightning_on_attack', name: '攻击闪电', description: '攻击时10%概率释放闪电', triggerChance: 10, triggerCondition: 'onAttack' }
    { id: 'heal_on_damage', name: '受伤治疗', description: '受伤时自动恢复50点生命', triggerCondition: 'onDamage', healAmount: 50 }
    { id: 'mana_on_critical', name: '暴击回魔', description: '暴击时恢复20点法力', triggerCondition: 'onCritical', manaAmount: 20 }
    { id: 'shield_on_low_health', name: '低血护盾', description: '生命值低于30%时激活护盾', triggerCondition: 'onLowHealth', threshold: 30 }
    { id: 'speed_on_kill', name: '击杀加速', description: '击杀敌人时获得速度加成', triggerCondition: 'onKill', bonusValue: 25, duration: 10 }
    { id: 'fire_aura_on_combat', name: '战斗火环', description: '进入战斗时激活火焰光环', triggerCondition: 'onCombatStart', damage: 10 }
    { id: 'thorns_on_hit', name: '荆棘反击', description: '被攻击时反弹25%伤害', triggerCondition: 'onHit', reflectPercent: 25 }
    { id: 'freeze_on_block', name: '格挡冰冻', description: '格挡时冰冻攻击者3秒', triggerCondition: 'onBlock', duration: 3 }
    { id: 'poison_on_critical', name: '暴击中毒', description: '暴击时使敌人中毒', triggerCondition: 'onCritical', duration: 10, damage: 5 }
    { id: 'rage_on_low_mana', name: '低魔狂怒', description: '法力值低于20%时攻击力+50%', triggerCondition: 'onLowMana', threshold: 20, bonusValue: 50 }
  ]

  // === 道具使用技能预设 (10种) ===
  itemUseSkills: [
    { id: 'restore_health_100', name: '恢复生命100点', description: '立即恢复100点生命值', effectType: 'instant', healAmount: 100 }
    { id: 'restore_health_500', name: '恢复生命500点', description: '立即恢复500点生命值', effectType: 'instant', healAmount: 500 }
    { id: 'restore_mana_200', name: '恢复法力200点', description: '立即恢复200点法力值', effectType: 'instant', manaAmount: 200 }
    { id: 'remove_all_debuffs', name: '移除所有负面状态', description: '清除所有负面效果', effectType: 'instant' }
    { id: 'temp_strength_boost', name: '临时力量+50', description: '力量+50，持续5分钟', effectType: 'overtime', bonusValue: 50, duration: 300 }
    { id: 'teleport_to_town', name: '传送回城', description: '瞬间传送到最近的城镇', effectType: 'instant', targetType: 'town' }
    { id: 'summon_merchant', name: '召唤商人', description: '召唤一个临时商人', effectType: 'summon', duration: 120 }
    { id: 'create_camp', name: '创建营地', description: '在当前位置创建临时营地', effectType: 'placement', duration: 600 }
    { id: 'mark_location', name: '标记位置', description: '在地图上标记当前位置', effectType: 'utility' }
    { id: 'unlock_door', name: '开锁', description: '打开指定的门锁', effectType: 'interaction', targetType: 'door' }
  ]

  // === 道具被动技能预设 (5种) ===
  itemPassiveSkills: [
    { id: 'auto_pickup', name: '自动拾取', description: '自动拾取附近的物品', effectRadius: 5, requiresEquipped: false }
    { id: 'movement_speed_10', name: '移动速度+10%', description: '持有时移动速度增加10%', bonusValue: 10, requiresEquipped: false }
    { id: 'luck_bonus', name: '幸运加成', description: '增加稀有物品掉落概率', bonusValue: 15, requiresEquipped: false }
    { id: 'exp_share', name: '经验共享', description: '队伍成员获得额外经验', bonusValue: 10, effectRadius: 20 }
    { id: 'detect_treasure', name: '宝藏探测', description: '显示附近的宝藏位置', effectRadius: 50, requiresEquipped: true }
  ]
}
```

### 2.2 物品模板预设系统
```typescript
interface ItemTemplatePresets {
  // === 装备类模板 (100个) ===
  equipmentTemplates: {
    // 武器模板 (25个)
    weapons: {
      // 近战武器 (15个)
      melee: [
        { id: 'sword_basic', name: '基础剑', type: '单手剑', size: [2, 12, 1], complexity: 'medium' }
        { id: 'sword_broad', name: '阔剑', type: '单手剑', size: [3, 12, 1], complexity: 'medium' }
        { id: 'sword_long', name: '长剑', type: '双手剑', size: [3, 16, 1], complexity: 'high' }
        { id: 'sword_curved', name: '弯刀', type: '单手剑', size: [2, 10, 1], complexity: 'medium' }
        { id: 'sword_rapier', name: '细剑', type: '单手剑', size: [1, 14, 1], complexity: 'low' }
        
        { id: 'axe_hand', name: '手斧', type: '单手斧', size: [3, 8, 1], complexity: 'medium' }
        { id: 'axe_battle', name: '战斧', type: '单手斧', size: [4, 10, 1], complexity: 'high' }
        { id: 'axe_great', name: '巨斧', type: '双手斧', size: [5, 14, 1], complexity: 'high' }
        
        { id: 'hammer_war', name: '战锤', type: '单手锤', size: [3, 10, 3], complexity: 'high' }
        { id: 'hammer_maul', name: '大锤', type: '双手锤', size: [4, 12, 4], complexity: 'high' }
        
        { id: 'dagger_basic', name: '匕首', type: '匕首', size: [1, 6, 1], complexity: 'low' }
        { id: 'dagger_curved', name: '弯匕首', type: '匕首', size: [1, 7, 1], complexity: 'medium' }
        
        { id: 'spear_basic', name: '长矛', type: '长矛', size: [1, 16, 1], complexity: 'medium' }
        { id: 'spear_halberd', name: '戟', type: '长矛', size: [3, 16, 1], complexity: 'high' }
        { id: 'spear_trident', name: '三叉戟', type: '长矛', size: [3, 14, 1], complexity: 'high' }
      ]
      
      // 远程武器 (10个)
      ranged: [
        { id: 'bow_short', name: '短弓', type: '弓', size: [1, 10, 2], complexity: 'medium' }
        { id: 'bow_long', name: '长弓', type: '弓', size: [1, 14, 2], complexity: 'medium' }
        { id: 'bow_composite', name: '复合弓', type: '弓', size: [1, 12, 3], complexity: 'high' }
        { id: 'bow_recurve', name: '反曲弓', type: '弓', size: [1, 11, 2], complexity: 'high' }
        
        { id: 'crossbow_light', name: '轻弩', type: '弩', size: [2, 8, 6], complexity: 'high' }
        { id: 'crossbow_heavy', name: '重弩', type: '弩', size: [3, 10, 8], complexity: 'high' }
        
        { id: 'throwing_knife', name: '飞刀', type: '投掷', size: [1, 4, 1], complexity: 'low' }
        { id: 'throwing_axe', name: '飞斧', type: '投掷', size: [2, 6, 1], complexity: 'medium' }
        { id: 'throwing_star', name: '手里剑', type: '投掷', size: [3, 1, 3], complexity: 'medium' }
        { id: 'javelin', name: '标枪', type: '投掷', size: [1, 12, 1], complexity: 'medium' }
      ]
    }
    
    // 护甲模板 (40个)
    armor: {
      // 头部护甲 (10个)
      helmets: [
        { id: 'helm_basic', name: '基础头盔', type: '头盔', size: [6, 6, 6], complexity: 'medium' }
        { id: 'helm_full', name: '全盔', type: '头盔', size: [6, 7, 6], complexity: 'high' }
        { id: 'helm_open', name: '开面盔', type: '头盔', size: [6, 6, 6], complexity: 'medium' }
        { id: 'helm_barbute', name: '巴比特盔', type: '头盔', size: [6, 6, 6], complexity: 'high' }
        { id: 'helm_sallet', name: '轻盔', type: '头盔', size: [6, 5, 6], complexity: 'medium' }
        { id: 'cap_leather', name: '皮帽', type: '帽子', size: [6, 4, 6], complexity: 'low' }
        { id: 'cap_cloth', name: '布帽', type: '帽子', size: [6, 3, 6], complexity: 'low' }
        { id: 'hood_basic', name: '兜帽', type: '帽子', size: [6, 5, 6], complexity: 'low' }
        { id: 'circlet_basic', name: '头环', type: '头饰', size: [6, 2, 6], complexity: 'medium' }
        { id: 'crown_simple', name: '简易王冠', type: '头饰', size: [6, 4, 6], complexity: 'high' }
      ]
      
      // 身体护甲 (10个)
      chestpieces: [
        { id: 'armor_plate', name: '板甲', type: '胸甲', size: [8, 10, 4], complexity: 'high' }
        { id: 'armor_chain', name: '锁甲', type: '胸甲', size: [8, 10, 3], complexity: 'high' }
        { id: 'armor_scale', name: '鳞甲', type: '胸甲', size: [8, 10, 3], complexity: 'high' }
        { id: 'armor_leather', name: '皮甲', type: '胸甲', size: [8, 10, 2], complexity: 'medium' }
        { id: 'armor_studded', name: '钉皮甲', type: '胸甲', size: [8, 10, 2], complexity: 'medium' }
        { id: 'robe_basic', name: '长袍', type: '长袍', size: [8, 12, 2], complexity: 'low' }
        { id: 'robe_mage', name: '法师袍', type: '长袍', size: [8, 12, 2], complexity: 'medium' }
        { id: 'tunic_basic', name: '束腰外衣', type: '夹克', size: [8, 8, 2], complexity: 'low' }
        { id: 'vest_leather', name: '皮背心', type: '背心', size: [8, 6, 2], complexity: 'low' }
        { id: 'shirt_cloth', name: '布衣', type: '衬衣', size: [8, 8, 1], complexity: 'low' }
      ]
      
      // 腿部护甲 (10个)
      legpieces: [
        { id: 'legs_plate', name: '板甲护腿', type: '腿甲', size: [6, 10, 3], complexity: 'high' }
        { id: 'legs_chain', name: '锁甲护腿', type: '腿甲', size: [6, 10, 2], complexity: 'high' }
        { id: 'legs_leather', name: '皮护腿', type: '腿甲', size: [6, 10, 2], complexity: 'medium' }
        { id: 'pants_cloth', name: '布裤', type: '裤子', size: [6, 10, 1], complexity: 'low' }
        { id: 'pants_leather', name: '皮裤', type: '裤子', size: [6, 10, 1], complexity: 'low' }
        { id: 'skirt_basic', name: '裙子', type: '裙子', size: [8, 6, 1], complexity: 'low' }
        { id: 'skirt_battle', name: '战裙', type: '裙子', size: [8, 6, 2], complexity: 'medium' }
        { id: 'kilt_basic', name: '短裙', type: '短裙', size: [6, 4, 1], complexity: 'low' }
        { id: 'shorts_cloth', name: '短裤', type: '短裤', size: [6, 4, 1], complexity: 'low' }
        { id: 'leggings_tight', name: '紧身裤', type: '紧身裤', size: [6, 10, 1], complexity: 'low' }
      ]
      
      // 脚部护甲 (10个)
      footwear: [
        { id: 'boots_plate', name: '板甲靴', type: '战靴', size: [4, 6, 8], complexity: 'high' }
        { id: 'boots_leather', name: '皮靴', type: '靴子', size: [4, 6, 8], complexity: 'medium' }
        { id: 'boots_cloth', name: '布靴', type: '靴子', size: [4, 5, 8], complexity: 'low' }
        { id: 'shoes_basic', name: '鞋子', type: '鞋子', size: [4, 3, 8], complexity: 'low' }
        { id: 'shoes_dress', name: '正装鞋', type: '鞋子', size: [4, 3, 8], complexity: 'medium' }
        { id: 'sandals_basic', name: '凉鞋', type: '凉鞋', size: [4, 2, 8], complexity: 'low' }
        { id: 'sandals_strapped', name: '绑带凉鞋', type: '凉鞋', size: [4, 3, 8], complexity: 'medium' }
        { id: 'slippers_soft', name: '软拖鞋', type: '拖鞋', size: [4, 2, 8], complexity: 'low' }
        { id: 'clogs_wooden', name: '木鞋', type: '木鞋', size: [4, 4, 8], complexity: 'medium' }
        { id: 'barefoot', name: '赤脚', type: '无鞋', size: [4, 1, 8], complexity: 'low' }
      ]
    }
    
    // 饰品模板 (20个)
    accessories: {
      // 戒指 (5个)
      rings: [
        { id: 'ring_simple', name: '简单戒指', type: '戒指', size: [2, 1, 2], complexity: 'low' }
        { id: 'ring_gem', name: '宝石戒指', type: '戒指', size: [2, 2, 2], complexity: 'medium' }
        { id: 'ring_signet', name: '印章戒指', type: '戒指', size: [3, 2, 2], complexity: 'medium' }
        { id: 'ring_band', name: '指环', type: '戒指', size: [2, 1, 2], complexity: 'low' }
        { id: 'ring_ornate', name: '华丽戒指', type: '戒指', size: [3, 2, 3], complexity: 'high' }
      ]
      
      // 项链 (5个)
      necklaces: [
        { id: 'necklace_chain', name: '链式项链', type: '项链', size: [1, 8, 1], complexity: 'medium' }
        { id: 'necklace_pendant', name: '吊坠项链', type: '项链', size: [2, 10, 1], complexity: 'medium' }
        { id: 'amulet_basic', name: '护身符', type: '护身符', size: [3, 3, 1], complexity: 'medium' }
        { id: 'amulet_ornate', name: '华丽护身符', type: '护身符', size: [4, 4, 1], complexity: 'high' }
        { id: 'choker_simple', name: '颈圈', type: '颈圈', size: [1, 6, 1], complexity: 'low' }
      ]
      
      // 腰带 (5个)
      belts: [
        { id: 'belt_leather', name: '皮腰带', type: '腰带', size: [1, 8, 1], complexity: 'low' }
        { id: 'belt_studded', name: '钉皮腰带', type: '腰带', size: [1, 8, 1], complexity: 'medium' }
        { id: 'belt_chain', name: '链式腰带', type: '腰带', size: [1, 8, 1], complexity: 'medium' }
        { id: 'sash_cloth', name: '布腰带', type: '腰包', size: [2, 8, 2], complexity: 'low' }
        { id: 'girdle_ornate', name: '华丽腰带', type: '腰带', size: [2, 8, 1], complexity: 'high' }
      ]
      
      // 斗篷 (5个)
      cloaks: [
        { id: 'cloak_basic', name: '基础斗篷', type: '斗篷', size: [8, 10, 1], complexity: 'low' }
        { id: 'cloak_hooded', name: '连帽斗篷', type: '斗篷', size: [8, 12, 1], complexity: 'medium' }
        { id: 'cape_short', name: '短披风', type: '披风', size: [6, 6, 1], complexity: 'low' }
        { id: 'cape_long', name: '长披风', type: '披风', size: [8, 10, 1], complexity: 'medium' }
        { id: 'mantle_fur', name: '毛皮披肩', type: '外套', size: [6, 4, 2], complexity: 'medium' }
      ]
    }
    
    // 工具模板 (15个)
    tools: {
      // 采集工具 (8个)
      gathering: [
        { id: 'pickaxe_stone', name: '石镐', type: '镐', size: [3, 10, 1], complexity: 'medium' }
        { id: 'pickaxe_iron', name: '铁镐', type: '镐', size: [3, 10, 1], complexity: 'medium' }
        { id: 'pickaxe_steel', name: '钢镐', type: '镐', size: [3, 10, 1], complexity: 'high' }
        { id: 'axe_wood', name: '伐木斧', type: '斧', size: [3, 8, 1], complexity: 'medium' }
        { id: 'hoe_basic', name: '锄头', type: '锄头', size: [2, 8, 1], complexity: 'low' }
        { id: 'shovel_basic', name: '铲子', type: '铲子', size: [2, 10, 1], complexity: 'low' }
        { id: 'fishing_rod', name: '渔竿', type: '渔竿', size: [1, 12, 1], complexity: 'medium' }
        { id: 'sickle_basic', name: '镰刀', type: '镰刀', size: [2, 6, 1], complexity: 'medium' }
      ]
      
      // 制作工具 (4个)
      crafting: [
        { id: 'hammer_craft', name: '制作锤', type: '锤子', size: [2, 6, 2], complexity: 'medium' }
        { id: 'tongs_basic', name: '钳子', type: '钳子', size: [1, 8, 1], complexity: 'medium' }
        { id: 'chisel_basic', name: '刻刀', type: '刻刀', size: [1, 6, 1], complexity: 'low' }
        { id: 'needle_basic', name: '针线', type: '针线', size: [1, 4, 1], complexity: 'low' }
      ]
      
      // 实用工具 (3个)
      utility: [
        { id: 'torch_basic', name: '火把', type: '火把', size: [1, 8, 1], complexity: 'low' }
        { id: 'rope_coiled', name: '绳索', type: '绳索', size: [3, 3, 3], complexity: 'medium' }
        { id: 'ladder_portable', name: '梯子', type: '梯子', size: [2, 10, 1], complexity: 'medium' }
      ]
    }
  }
}
```

## 3. 物品生成向导系统

### 3.1 完整生成流程
```typescript
interface ItemGenerationWizard {
  // 第1步: 接收物品编辑器数据
  step1_ReceiveItemData: {
    itemType: string                       // 物品类型 (从物品编辑器传递)
    itemCategory: 'weapon' | 'armor' | 'accessory' | 'tool' | 'consumable' | 'functional' | 'material'
    itemSubType: string                    // 具体子类型 (剑、头盔、药水等)
    qualityLevel: 'poor' | 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
    itemLevel: number                      // 物品等级

    // 尺寸约束
    sizeConstraints: {
      maxVoxels: 300                       // 最大体素数量
      maxDimensions: [16, 16, 16]          // 最大尺寸 (小于世界方块)
      recommendedSize: [number, number, number] // 推荐尺寸
    }
  }

  // 第2步: 生成参数配置
  step2_GenerationConfig: {
    generationMode: 'template_based' | 'procedural' | 'hybrid'

    styleOptions: {
      materialStyle: 'medieval' | 'modern' | 'fantasy' | 'sci_fi' | 'steampunk'
      detailLevel: 'low' | 'medium' | 'high'           // 基于品质自动设置
      colorScheme: 'default' | 'warm' | 'cool' | 'monochrome' | 'vibrant'
    }

    variationSettings: {
      shapeVariation: number               // 形状变化程度 (0-100%)
      detailVariation: number              // 细节变化程度 (0-100%)
      colorVariation: number               // 颜色变化程度 (0-100%)
      sizeVariation: number                // 尺寸变化程度 (0-20%)
    }
  }

  // 第3步: 批量生成 (10个变体)
  step3_BatchGeneration: {
    generationCount: 10                    // 固定生成10个
    generationAlgorithm: {
      baseTemplate: VoxelTemplate          // 基础模板
      variationSeeds: number[]             // 10个不同的随机种子
      qualityModifiers: QualityModifier[]  // 品质修正器
    }

    generationResults: {
      variants: VoxelModel[]               // 10个生成的变体
      metadata: {
        voxelCount: number                 // 体素数量
        dimensions: [number, number, number] // 实际尺寸
        complexity: number                 // 复杂度评分
        performance: number                // 性能评分
      }[]
    }
  }

  // 第4步: 变体选择和预览
  step4_VariantSelection: {
    previewGrid: {
      layout: '2x5'                        // 2行5列显示10个变体
      previewMode: '3d_turntable'          // 3D旋转预览
      selectionMode: 'single'              // 单选模式
    }

    previewFeatures: {
      realTimeRotation: boolean            // 实时旋转
      qualityPreview: boolean              // 品质效果预览
      iconPreview: boolean                 // 图标预览 (45°倾斜)
      sizeComparison: boolean              // 尺寸对比
    }

    selectionTools: {
      favoriteMarking: boolean             // 收藏标记
      quickComparison: boolean             // 快速对比
      detailInspection: boolean            // 细节检查
    }
  }

  // 第5步: 手动修改 (可选)
  step5_ManualEditing: {
    editingMode: 'item_optimized'          // 物品优化编辑模式

    itemSpecificTools: {
      symmetryTools: {
        enabled: boolean                   // 对称工具
        symmetryAxis: 'x' | 'y' | 'z' | 'xy' | 'xz' | 'yz'
        autoSymmetry: boolean              // 自动对称
      }

      proportionGuides: {
        enabled: boolean                   // 比例指南
        itemTypeGuides: boolean            // 物品类型指南
        goldenRatio: boolean               // 黄金比例指南
      }

      detailBrushes: {
        textureCarving: boolean            // 纹理雕刻
        edgeSharpening: boolean            // 边缘锐化
        surfaceSmoothing: boolean          // 表面平滑
      }

      qualityEnhancement: {
        autoDetailAdd: boolean             // 自动添加细节
        qualityUpgrade: boolean            // 品质升级
        materialUpgrade: boolean           // 材质升级
      }
    }

    realTimeValidation: {
      voxelCountCheck: boolean             // 体素数量检查
      sizeConstraintCheck: boolean         // 尺寸约束检查
      performanceCheck: boolean            // 性能检查
      iconPreview: boolean                 // 图标实时预览
    }
  }

  // 第6步: 图标生成和导出
  step6_IconAndExport: {
    iconGeneration: {
      autoGenerate: boolean                // 自动生成图标
      angle: {
        zRotation: 45                      // Z轴倾斜45度
        xRotation: 0                       // X轴旋转
        yRotation: 0                       // Y轴旋转
      }
      iconSize: [64, 64]                   // 图标尺寸 (像素)
      backgroundColor: 'transparent'       // 背景透明
      lighting: 'standard'                 // 标准光照
    }

    exportOptions: {
      modelFormat: '.voxel'                // 体素模型格式
      iconFormat: '.png'                   // 图标格式
      metadataIncluded: boolean            // 包含元数据
      compressionLevel: number             // 压缩等级
    }

    assetLibraryIntegration: {
      autoSaveToLibrary: boolean           // 自动保存到资产库
      categoryTagging: boolean             // 分类标签
      searchKeywords: string[]             // 搜索关键词
    }
  }
}
```

## 4. 游戏资产库系统

### 4.1 统一资产管理
```typescript
interface GameAssetLibrary {
  // 资产库主界面
  libraryInterface: {
    categories: [
      {
        id: 'models'
        name: '模型库'
        icon: 'model_icon'
        subcategories: [
          { id: 'item_models', name: '物品模型', count: 200, description: '装备和道具的3D模型' }
          { id: 'building_models', name: '建筑模型', count: 150, description: '建筑和结构模型' }
          { id: 'character_models', name: '角色模型', count: 100, description: '角色和生物模型' }
          { id: 'environment_models', name: '环境模型', count: 300, description: '环境装饰模型' }
        ]
      }
      {
        id: 'scripts'
        name: '脚本库'
        icon: 'script_icon'
        subcategories: [
          { id: 'item_scripts', name: '物品脚本', count: 50, description: '##物品脚本## 专用脚本' }
          { id: 'skill_scripts', name: '技能脚本', count: 100, description: '##技能脚本## 专用脚本' }
          { id: 'life_scripts', name: '生活系统脚本', count: 30, description: '##生活系统脚本## 专用脚本' }
          { id: 'unit_scripts', name: '单位脚本', count: 40, description: '##单位脚本## 专用脚本' }
          { id: 'global_scripts', name: '全局脚本', count: 20, description: '##全局脚本## 通用脚本' }
        ]
      }
      {
        id: 'animations'
        name: '动画库'
        icon: 'animation_icon'
        subcategories: [
          { id: 'character_anims', name: '角色动画', count: 200, description: '角色动作动画' }
          { id: 'item_anims', name: '物品动画', count: 80, description: '物品使用动画' }
          { id: 'effect_anims', name: '特效动画', count: 120, description: '特效动画序列' }
          { id: 'building_anims', name: '建筑动画', count: 50, description: '建筑相关动画' }
        ]
      }
      {
        id: 'effects'
        name: '特效库'
        icon: 'effect_icon'
        subcategories: [
          { id: 'particle_effects', name: '粒子特效', count: 150, description: '粒子系统特效' }
          { id: 'lighting_effects', name: '光照特效', count: 80, description: '光照和阴影特效' }
          { id: 'post_effects', name: '后处理特效', count: 60, description: '屏幕后处理特效' }
          { id: 'item_effects', name: '物品特效', count: 100, description: '物品专用特效' }
        ]
      }
      {
        id: 'audio'
        name: '音频库'
        icon: 'audio_icon'
        subcategories: [
          { id: 'sound_effects', name: '音效', count: 300, description: '游戏音效' }
          { id: 'music', name: '音乐', count: 50, description: '背景音乐' }
          { id: 'ambient', name: '环境音', count: 100, description: '环境氛围音效' }
          { id: 'voice', name: '语音', count: 200, description: '角色语音' }
        ]
      }
    ]
  }

  // 物品模型库详细设计
  itemModelLibrary: {
    organization: {
      byCategory: {
        weapons: { count: 25, models: ItemModel[] }
        armor: { count: 40, models: ItemModel[] }
        accessories: { count: 20, models: ItemModel[] }
        tools: { count: 15, models: ItemModel[] }
        consumables: { count: 40, models: ItemModel[] }
        functional: { count: 30, models: ItemModel[] }
        materials: { count: 20, models: ItemModel[] }
        special: { count: 10, models: ItemModel[] }
      }

      byQuality: {
        poor: { count: 30, models: ItemModel[] }
        common: { count: 50, models: ItemModel[] }
        uncommon: { count: 40, models: ItemModel[] }
        rare: { count: 35, models: ItemModel[] }
        epic: { count: 25, models: ItemModel[] }
        legendary: { count: 20, models: ItemModel[] }
      }

      byStyle: {
        medieval: { count: 60, models: ItemModel[] }
        fantasy: { count: 50, models: ItemModel[] }
        modern: { count: 40, models: ItemModel[] }
        sci_fi: { count: 30, models: ItemModel[] }
        steampunk: { count: 20, models: ItemModel[] }
      }
    }

    searchAndFilter: {
      textSearch: {
        enabled: boolean                   // 文本搜索
        searchFields: ['name', 'description', 'tags', 'category']
        fuzzySearch: boolean               // 模糊搜索
      }

      advancedFilters: {
        categoryFilter: string[]           // 分类筛选
        qualityFilter: string[]            // 品质筛选
        sizeFilter: {                      // 尺寸筛选
          minSize: [number, number, number]
          maxSize: [number, number, number]
        }
        complexityFilter: {                // 复杂度筛选
          minComplexity: number
          maxComplexity: number
        }
        dateFilter: {                      // 创建日期筛选
          startDate: Date
          endDate: Date
        }
        authorFilter: string[]             // 作者筛选
      }
    }

    previewFeatures: {
      thumbnailGrid: {
        enabled: boolean                   // 缩略图网格
        gridSize: 'small' | 'medium' | 'large'
        showMetadata: boolean              // 显示元数据
      }

      quickPreview: {
        enabled: boolean                   // 快速预览
        hoverPreview: boolean              // 悬停预览
        rotationPreview: boolean           // 旋转预览
      }

      detailedView: {
        enabled: boolean                   // 详细视图
        show3DModel: boolean               // 显示3D模型
        showProperties: boolean            // 显示属性
        showHistory: boolean               // 显示历史
      }

      comparisonMode: {
        enabled: boolean                   // 对比模式
        maxCompareItems: 4                 // 最大对比数量
        sideBySideView: boolean            // 并排视图
      }
    }

    managementFeatures: {
      favoriteSystem: {
        enabled: boolean                   // 收藏系统
        favoriteCategories: string[]       // 收藏分类
        sharedFavorites: boolean           // 共享收藏
      }

      ratingSystem: {
        enabled: boolean                   // 评分系统
        ratingScale: number                // 评分范围 (1-5)
        averageRating: boolean             // 平均评分
        userReviews: boolean               // 用户评价
      }

      tagSystem: {
        enabled: boolean                   // 标签系统
        customTags: boolean                // 自定义标签
        tagSuggestions: boolean            // 标签建议
        tagHierarchy: boolean              // 标签层级
      }

      versionControl: {
        enabled: boolean                   // 版本控制
        autoVersioning: boolean            // 自动版本控制
        versionComparison: boolean         // 版本对比
        rollbackSupport: boolean           // 回滚支持
      }

      shareExport: {
        enabled: boolean                   // 分享导出
        exportFormats: string[]            // 导出格式
        cloudSync: boolean                 // 云同步
        teamSharing: boolean               // 团队分享
      }
    }
  }
}
```

## 5. 编辑器集成系统

### 5.1 数据传递和跳转机制
```typescript
interface EditorIntegration {
  // 物品编辑器 → 体素编辑器
  itemToVoxelData: {
    itemBasicInfo: {
      itemId: string
      itemName: string
      itemType: string
      itemCategory: string
      itemSubType: string
    }

    visualRequirements: {
      qualityLevel: string
      itemLevel: number
      sizeConstraints: [number, number, number]
      materialHints: string[]
      colorHints: string[]
    }

    functionalRequirements: {
      isContainer: boolean                 // 是否为容器 (影响开口设计)
      isWeapon: boolean                    // 是否为武器 (影响握持设计)
      isTool: boolean                      // 是否为工具 (影响功能部位设计)
      specialFeatures: string[]            // 特殊功能需求
    }
  }

  // 体素编辑器 → 物品编辑器
  voxelToItemData: {
    modelData: {
      voxelModel: VoxelModelData           // 体素模型数据
      modelMetadata: {
        voxelCount: number
        dimensions: [number, number, number]
        complexity: number
        performance: number
      }
    }

    iconData: {
      iconImage: ImageData                 // 45°倾斜图标
      iconMetadata: {
        size: [number, number]
        format: string
        generationTime: Date
      }
    }

    assetReferences: {
      modelLibraryId: string               // 模型库中的ID
      iconLibraryId: string                // 图标库中的ID
      relatedAssets: string[]              // 相关资产ID
    }
  }

  // 跳转调用机制
  editorJumpSystem: {
    jumpTrigger: {
      fromItemEditor: boolean              // 从物品编辑器跳转
      jumpButton: '创建3D模型' | '编辑3D模型' | '选择模型'
      jumpContext: 'create' | 'edit' | 'select'
    }

    contextPreservation: {
      itemEditorState: ItemEditorState     // 保存物品编辑器状态
      returnCallback: Function             // 返回回调函数
      dataExchangeBuffer: DataBuffer       // 数据交换缓冲区
    }

    voxelEditorMode: {
      specialMode: 'item_generation'       // 特殊的物品生成模式
      uiAdaptation: boolean                // UI适配
      toolsetRestriction: boolean          // 工具集限制
      autoReturn: boolean                  // 自动返回
    }
  }
}
```

### 5.2 用户体验流程
```typescript
interface UserExperienceFlow {
  // 完整的使用流程
  completeWorkflow: {
    step1: {
      action: '物品编辑器中点击"创建3D模型"'
      trigger: 'user_click'
      result: '跳转到体素编辑器物品生成模式'
    }

    step2: {
      action: '体素编辑器接收物品数据'
      trigger: 'data_transfer'
      result: '显示物品生成向导界面'
    }

    step3: {
      action: '系统生成10个模型变体'
      trigger: 'auto_generation'
      result: '2x5网格显示所有变体'
    }

    step4: {
      action: '用户选择满意的变体'
      trigger: 'user_selection'
      result: '选中变体高亮显示'
    }

    step5: {
      action: '可选手动精修模型'
      trigger: 'user_choice'
      result: '进入物品优化编辑模式'
    }

    step6: {
      action: '系统自动生成45°图标'
      trigger: 'auto_icon_generation'
      result: '生成透明背景PNG图标'
    }

    step7: {
      action: '保存到游戏资产库'
      trigger: 'auto_save'
      result: '模型和图标存入对应分类'
    }

    step8: {
      action: '返回物品编辑器'
      trigger: 'auto_return'
      result: '携带模型数据返回'
    }

    step9: {
      action: '物品编辑器绑定模型'
      trigger: 'auto_binding'
      result: '完成3D模型和图标绑定'
    }
  }

  // 性能优化
  performanceOptimization: {
    modelGeneration: {
      batchProcessing: boolean             // 批量处理
      backgroundGeneration: boolean        // 后台生成
      progressIndicator: boolean           // 进度指示器
      cancelSupport: boolean               // 取消支持
    }

    previewOptimization: {
      lodPreview: boolean                  // LOD预览
      thumbnailCaching: boolean            // 缩略图缓存
      lazyLoading: boolean                 // 懒加载
      viewportCulling: boolean             // 视口剔除
    }

    memoryManagement: {
      modelPooling: boolean                // 模型池化
      textureCompression: boolean          // 纹理压缩
      garbageCollection: boolean           // 垃圾回收
      memoryLimit: number                  // 内存限制 (MB)
    }
  }
}
```

---

## 总结

体素编辑器物品生成系统作为物品编辑器的深度集成模块，提供了：

### 核心功能
- **智能批量生成**: 一次生成10个高质量变体供用户选择
- **200个物品模板**: 100个装备模板 + 100个道具模板，涵盖所有物品类型
- **50种物品技能预设**: 装备被动、主动、触发技能和道具使用、被动技能
- **自动图标生成**: Z轴45°倾斜自动生成透明背景图标
- **300体素限制**: 确保性能和合理的物品尺寸

### 技术创新
- **6步生成向导**: 从数据接收到导出完成的完整流程
- **物品专用工具**: 对称工具、比例指南、细节雕刻等专用编辑工具
- **实时验证**: 体素数量、尺寸约束、性能检查的实时验证
- **品质驱动**: 品质等级自动影响模型复杂度和细节程度
- **无缝跳转**: 编辑器间保持上下文的无缝跳转机制

### 游戏资产库
- **统一管理**: 模型、脚本、动画、特效、音频的统一资产管理
- **智能分类**: 按类型、品质、风格、作者等多维度分类
- **高级搜索**: 文本搜索、高级筛选、标签系统的完整搜索功能
- **版本控制**: 自动版本控制、版本对比、回滚支持
- **团队协作**: 收藏系统、评分系统、分享导出功能

### 用户体验
- **模板丰富**: 200个预设模板覆盖所有常见物品类型
- **操作简单**: 一键生成、可视化选择、自动处理
- **高度可定制**: 支持手动精修和参数调整
- **性能优化**: 批量处理、缓存机制、内存管理
- **完整集成**: 与物品编辑器的完美集成体验

### 适用场景
- **ARPG游戏**: 丰富的装备和道具模型生成
- **生存游戏**: 工具、食物、材料的快速建模
- **建造游戏**: 建造工具和装饰物品的生成
- **RPG游戏**: 复杂装备套装的批量生成
- **独立开发**: 快速原型和资产生成

**注意**:
- 所有生成的模型都小于世界方块尺寸，保持游戏比例协调
- 自动图标生成使用标准45°倾斜角度，确保视觉一致性
- 游戏资产库支持团队协作和版本管理
- 与物品编辑器的跳转机制保持上下文完整性
```
