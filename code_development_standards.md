# 体素游戏编辑器代码开发规范

## 1. 总体原则

### 1.1 开发理念
- **质量优先**: 代码质量比开发速度更重要
- **可维护性**: 编写易于理解和维护的代码
- **性能意识**: 时刻考虑性能影响，特别是3D渲染和大数据处理
- **用户体验**: 所有技术决策都要考虑最终用户体验

### 1.2 核心要求
- 所有代码必须通过TypeScript严格模式检查
- 所有功能必须有对应的单元测试
- 所有公共API必须有完整的文档注释
- 所有性能关键代码必须有性能测试

## 2. 项目结构规范

### 2.1 目录结构
```
voxel-game-editor/
├── src/
│   ├── main/                    # Electron主进程
│   │   ├── main.ts
│   │   ├── menu.ts
│   │   └── window-manager.ts
│   ├── renderer/                # React渲染进程
│   │   ├── components/          # React组件
│   │   │   ├── common/          # 通用组件
│   │   │   ├── voxel-editor/    # 体素编辑器组件
│   │   │   ├── item-editor/     # 物品编辑器组件
│   │   │   └── ui/              # UI组件
│   │   ├── stores/              # Redux状态管理
│   │   │   ├── voxel/           # 体素编辑器状态
│   │   │   ├── item/            # 物品编辑器状态
│   │   │   └── app/             # 应用全局状态
│   │   ├── services/            # 业务逻辑服务
│   │   │   ├── voxel-engine/    # 体素引擎服务
│   │   │   ├── file-system/     # 文件系统服务
│   │   │   └── asset-manager/   # 资产管理服务
│   │   ├── utils/               # 工具函数
│   │   │   ├── math/            # 数学工具
│   │   │   ├── three/           # Three.js工具
│   │   │   └── validation/      # 验证工具
│   │   ├── types/               # TypeScript类型定义
│   │   │   ├── voxel.ts         # 体素相关类型
│   │   │   ├── item.ts          # 物品相关类型
│   │   │   └── common.ts        # 通用类型
│   │   └── constants/           # 常量定义
│   ├── assets/                  # 静态资源
│   │   ├── icons/               # 图标
│   │   ├── textures/            # 纹理
│   │   └── models/              # 预设模型
│   └── tests/                   # 测试文件
│       ├── unit/                # 单元测试
│       ├── integration/         # 集成测试
│       └── e2e/                 # 端到端测试
├── docs/                        # 文档
├── scripts/                     # 构建脚本
└── config/                      # 配置文件
```

### 2.2 文件命名规范
- **组件文件**: PascalCase，如 `VoxelEditor.tsx`
- **服务文件**: kebab-case，如 `voxel-engine.service.ts`
- **工具文件**: kebab-case，如 `math-utils.ts`
- **类型文件**: kebab-case，如 `voxel-types.ts`
- **常量文件**: UPPER_SNAKE_CASE，如 `VOXEL_CONSTANTS.ts`

## 3. TypeScript编码规范

### 3.1 类型定义
```typescript
// ✅ 正确：使用interface定义对象类型
interface VoxelModel {
  readonly id: string;
  readonly name: string;
  readonly dimensions: readonly [number, number, number];
  readonly voxels: ReadonlyArray<Voxel>;
  readonly metadata: VoxelModelMetadata;
}

// ✅ 正确：使用type定义联合类型
type VoxelMaterial = 'stone' | 'wood' | 'metal' | 'glass' | 'custom';

// ✅ 正确：使用泛型提高复用性
interface ApiResponse<T> {
  readonly success: boolean;
  readonly data: T;
  readonly error?: string;
}

// ❌ 错误：使用any类型
const badData: any = {};

// ✅ 正确：使用具体类型或unknown
const goodData: VoxelModel = createVoxelModel();
const unknownData: unknown = parseJsonData();
```

### 3.2 函数定义
```typescript
// ✅ 正确：纯函数，有明确的输入输出类型
function calculateVoxelVolume(dimensions: readonly [number, number, number]): number {
  const [width, height, depth] = dimensions;
  return width * height * depth;
}

// ✅ 正确：异步函数使用Promise类型
async function loadVoxelModel(id: string): Promise<VoxelModel> {
  const response = await fetch(`/api/models/${id}`);
  if (!response.ok) {
    throw new Error(`Failed to load model: ${response.statusText}`);
  }
  return response.json();
}

// ✅ 正确：使用函数重载处理多种参数情况
function createVoxel(position: Vector3): Voxel;
function createVoxel(x: number, y: number, z: number): Voxel;
function createVoxel(positionOrX: Vector3 | number, y?: number, z?: number): Voxel {
  // 实现逻辑
}
```

### 3.3 类定义
```typescript
// ✅ 正确：使用readonly修饰符保护数据
class VoxelEngine {
  private readonly scene: THREE.Scene;
  private readonly renderer: THREE.WebGLRenderer;
  private readonly camera: THREE.PerspectiveCamera;
  
  constructor(canvas: HTMLCanvasElement) {
    this.scene = new THREE.Scene();
    this.renderer = new THREE.WebGLRenderer({ canvas });
    this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
  }
  
  // ✅ 正确：公共方法有完整的JSDoc注释
  /**
   * 添加体素到场景中
   * @param voxel - 要添加的体素对象
   * @param position - 体素在3D空间中的位置
   * @returns 添加成功返回true，否则返回false
   */
  public addVoxel(voxel: Voxel, position: Vector3): boolean {
    // 实现逻辑
    return true;
  }
  
  // ✅ 正确：私有方法处理内部逻辑
  private updateRenderer(): void {
    this.renderer.render(this.scene, this.camera);
  }
}
```

## 4. React组件规范

### 4.1 函数组件
```typescript
// ✅ 正确：使用函数组件和hooks
interface VoxelEditorProps {
  readonly model: VoxelModel;
  readonly onModelChange: (model: VoxelModel) => void;
  readonly isReadOnly?: boolean;
}

const VoxelEditor: React.FC<VoxelEditorProps> = ({
  model,
  onModelChange,
  isReadOnly = false
}) => {
  // ✅ 正确：使用useCallback优化性能
  const handleVoxelAdd = useCallback((position: Vector3, material: VoxelMaterial) => {
    if (isReadOnly) return;
    
    const newModel = addVoxelToModel(model, position, material);
    onModelChange(newModel);
  }, [model, onModelChange, isReadOnly]);
  
  // ✅ 正确：使用useMemo优化计算
  const voxelCount = useMemo(() => model.voxels.length, [model.voxels]);
  
  return (
    <div className="voxel-editor">
      <div className="voxel-editor__info">
        体素数量: {voxelCount}
      </div>
      <VoxelCanvas
        model={model}
        onVoxelAdd={handleVoxelAdd}
        readOnly={isReadOnly}
      />
    </div>
  );
};

export default VoxelEditor;
```

### 4.2 自定义Hooks
```typescript
// ✅ 正确：自定义hook封装复杂逻辑
function useVoxelEngine(canvas: HTMLCanvasElement | null) {
  const [engine, setEngine] = useState<VoxelEngine | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    if (!canvas) return;
    
    try {
      const newEngine = new VoxelEngine(canvas);
      setEngine(newEngine);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
    
    return () => {
      engine?.dispose();
    };
  }, [canvas]);
  
  return { engine, isLoading, error };
}
```

## 5. 状态管理规范

### 5.1 Redux Toolkit使用
```typescript
// ✅ 正确：使用createSlice创建状态切片
interface VoxelEditorState {
  readonly currentModel: VoxelModel | null;
  readonly selectedTool: VoxelTool;
  readonly isLoading: boolean;
  readonly error: string | null;
}

const initialState: VoxelEditorState = {
  currentModel: null,
  selectedTool: 'brush',
  isLoading: false,
  error: null,
};

const voxelEditorSlice = createSlice({
  name: 'voxelEditor',
  initialState,
  reducers: {
    setCurrentModel: (state, action: PayloadAction<VoxelModel>) => {
      state.currentModel = action.payload;
    },
    setSelectedTool: (state, action: PayloadAction<VoxelTool>) => {
      state.selectedTool = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setCurrentModel, setSelectedTool, setLoading, setError } = voxelEditorSlice.actions;
export default voxelEditorSlice.reducer;
```

### 5.2 异步操作
```typescript
// ✅ 正确：使用createAsyncThunk处理异步操作
export const loadVoxelModel = createAsyncThunk(
  'voxelEditor/loadModel',
  async (modelId: string, { rejectWithValue }) => {
    try {
      const model = await VoxelModelService.loadModel(modelId);
      return model;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

// 在slice中处理异步操作
const voxelEditorSlice = createSlice({
  name: 'voxelEditor',
  initialState,
  reducers: {
    // 同步reducers
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadVoxelModel.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadVoxelModel.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentModel = action.payload;
      })
      .addCase(loadVoxelModel.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});
```

## 6. Three.js和3D编程规范

### 6.1 场景管理
```typescript
// ✅ 正确：封装Three.js场景管理
class VoxelScene {
  private readonly scene: THREE.Scene;
  private readonly renderer: THREE.WebGLRenderer;
  private readonly camera: THREE.PerspectiveCamera;
  private readonly controls: OrbitControls;

  constructor(canvas: HTMLCanvasElement) {
    this.scene = new THREE.Scene();
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: true,
      alpha: true
    });
    this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    this.controls = new OrbitControls(this.camera, canvas);

    this.setupLighting();
    this.setupControls();
  }

  private setupLighting(): void {
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);

    this.scene.add(ambientLight);
    this.scene.add(directionalLight);
  }

  // ✅ 正确：提供清理方法防止内存泄漏
  public dispose(): void {
    this.controls.dispose();
    this.renderer.dispose();

    // 清理所有几何体和材质
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose();
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });
  }
}
```

### 6.2 体素渲染优化
```typescript
// ✅ 正确：使用实例化渲染优化性能
class VoxelRenderer {
  private readonly instancedMesh: THREE.InstancedMesh;
  private readonly maxInstances = 100000;

  constructor() {
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshLambertMaterial();

    this.instancedMesh = new THREE.InstancedMesh(
      geometry,
      material,
      this.maxInstances
    );

    // 设置实例矩阵
    this.instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
  }

  public updateVoxels(voxels: ReadonlyArray<Voxel>): void {
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color();

    voxels.forEach((voxel, index) => {
      if (index >= this.maxInstances) return;

      // 设置位置和缩放
      matrix.setPosition(voxel.position.x, voxel.position.y, voxel.position.z);
      this.instancedMesh.setMatrixAt(index, matrix);

      // 设置颜色
      color.setHex(voxel.color);
      this.instancedMesh.setColorAt(index, color);
    });

    this.instancedMesh.instanceMatrix.needsUpdate = true;
    if (this.instancedMesh.instanceColor) {
      this.instancedMesh.instanceColor.needsUpdate = true;
    }

    this.instancedMesh.count = Math.min(voxels.length, this.maxInstances);
  }
}
```

## 7. 性能优化规范

### 7.1 内存管理
```typescript
// ✅ 正确：使用对象池减少GC压力
class Vector3Pool {
  private static readonly pool: THREE.Vector3[] = [];
  private static readonly maxPoolSize = 1000;

  public static acquire(): THREE.Vector3 {
    return this.pool.pop() || new THREE.Vector3();
  }

  public static release(vector: THREE.Vector3): void {
    if (this.pool.length < this.maxPoolSize) {
      vector.set(0, 0, 0);
      this.pool.push(vector);
    }
  }

  public static clear(): void {
    this.pool.length = 0;
  }
}

// ✅ 正确：在组件中使用对象池
const VoxelCanvas: React.FC<VoxelCanvasProps> = ({ model }) => {
  useEffect(() => {
    const tempVector = Vector3Pool.acquire();

    // 使用tempVector进行计算

    return () => {
      Vector3Pool.release(tempVector);
    };
  }, [model]);
};
```

### 7.2 渲染优化
```typescript
// ✅ 正确：使用LOD系统优化渲染
class VoxelLODManager {
  private readonly lodLevels: THREE.LOD[] = [];

  public createLODObject(voxelModel: VoxelModel): THREE.LOD {
    const lod = new THREE.LOD();

    // 高细节级别 (近距离)
    const highDetail = this.createHighDetailMesh(voxelModel);
    lod.addLevel(highDetail, 0);

    // 中等细节级别 (中距离)
    const mediumDetail = this.createMediumDetailMesh(voxelModel);
    lod.addLevel(mediumDetail, 50);

    // 低细节级别 (远距离)
    const lowDetail = this.createLowDetailMesh(voxelModel);
    lod.addLevel(lowDetail, 200);

    return lod;
  }

  private createHighDetailMesh(model: VoxelModel): THREE.Mesh {
    // 创建高细节网格
    return new THREE.Mesh();
  }
}
```

## 8. 错误处理规范

### 8.1 错误类型定义
```typescript
// ✅ 正确：定义具体的错误类型
abstract class VoxelEditorError extends Error {
  abstract readonly code: string;
  abstract readonly category: 'validation' | 'rendering' | 'file' | 'network';
}

class VoxelValidationError extends VoxelEditorError {
  readonly code = 'VOXEL_VALIDATION_ERROR';
  readonly category = 'validation' as const;

  constructor(message: string, public readonly field: string) {
    super(message);
    this.name = 'VoxelValidationError';
  }
}

class VoxelRenderingError extends VoxelEditorError {
  readonly code = 'VOXEL_RENDERING_ERROR';
  readonly category = 'rendering' as const;

  constructor(message: string, public readonly context?: string) {
    super(message);
    this.name = 'VoxelRenderingError';
  }
}
```

### 8.2 错误处理模式
```typescript
// ✅ 正确：使用Result模式处理可能失败的操作
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

async function loadVoxelModelSafe(id: string): Promise<Result<VoxelModel, VoxelEditorError>> {
  try {
    const model = await VoxelModelService.loadModel(id);
    return { success: true, data: model };
  } catch (error) {
    if (error instanceof VoxelEditorError) {
      return { success: false, error };
    }
    return {
      success: false,
      error: new VoxelValidationError('Unknown error occurred', 'model')
    };
  }
}

// ✅ 正确：在组件中处理Result
const VoxelModelLoader: React.FC<{ modelId: string }> = ({ modelId }) => {
  const [result, setResult] = useState<Result<VoxelModel, VoxelEditorError> | null>(null);

  useEffect(() => {
    loadVoxelModelSafe(modelId).then(setResult);
  }, [modelId]);

  if (!result) return <div>Loading...</div>;

  if (!result.success) {
    return <div>Error: {result.error.message}</div>;
  }

  return <VoxelEditor model={result.data} />;
};
```

## 9. 测试规范

### 9.1 单元测试
```typescript
// ✅ 正确：测试纯函数
describe('VoxelUtils', () => {
  describe('calculateVoxelVolume', () => {
    it('should calculate volume correctly for positive dimensions', () => {
      const dimensions: readonly [number, number, number] = [2, 3, 4];
      const result = calculateVoxelVolume(dimensions);
      expect(result).toBe(24);
    });

    it('should handle zero dimensions', () => {
      const dimensions: readonly [number, number, number] = [0, 3, 4];
      const result = calculateVoxelVolume(dimensions);
      expect(result).toBe(0);
    });

    it('should throw error for negative dimensions', () => {
      const dimensions: readonly [number, number, number] = [-1, 3, 4];
      expect(() => calculateVoxelVolume(dimensions)).toThrow();
    });
  });
});
```

### 9.2 组件测试
```typescript
// ✅ 正确：测试React组件
describe('VoxelEditor', () => {
  const mockModel: VoxelModel = {
    id: 'test-model',
    name: 'Test Model',
    dimensions: [10, 10, 10],
    voxels: [],
    metadata: { createdAt: new Date(), version: '1.0' }
  };

  it('should render voxel count correctly', () => {
    const onModelChange = jest.fn();

    render(
      <VoxelEditor
        model={mockModel}
        onModelChange={onModelChange}
      />
    );

    expect(screen.getByText('体素数量: 0')).toBeInTheDocument();
  });

  it('should call onModelChange when voxel is added', async () => {
    const onModelChange = jest.fn();

    render(
      <VoxelEditor
        model={mockModel}
        onModelChange={onModelChange}
      />
    );

    // 模拟添加体素操作
    const canvas = screen.getByRole('img'); // Canvas元素
    fireEvent.click(canvas, { clientX: 100, clientY: 100 });

    await waitFor(() => {
      expect(onModelChange).toHaveBeenCalled();
    });
  });
});
```

## 10. 文档和注释规范

### 10.1 JSDoc注释
```typescript
/**
 * 体素模型管理服务
 *
 * 提供体素模型的创建、加载、保存和验证功能。
 * 支持多种文件格式的导入导出。
 *
 * @example
 * ```typescript
 * const service = new VoxelModelService();
 * const model = await service.loadModel('model-id');
 * const isValid = service.validateModel(model);
 * ```
 */
class VoxelModelService {
  /**
   * 加载指定ID的体素模型
   *
   * @param id - 模型的唯一标识符
   * @param options - 加载选项
   * @param options.includeMetadata - 是否包含元数据，默认为true
   * @param options.validateOnLoad - 是否在加载时验证，默认为true
   * @returns Promise，解析为加载的体素模型
   *
   * @throws {VoxelValidationError} 当模型ID无效时抛出
   * @throws {VoxelRenderingError} 当模型数据损坏时抛出
   *
   * @example
   * ```typescript
   * try {
   *   const model = await service.loadModel('my-model', {
   *     includeMetadata: false,
   *     validateOnLoad: true
   *   });
   *   console.log(`Loaded model: ${model.name}`);
   * } catch (error) {
   *   console.error('Failed to load model:', error.message);
   * }
   * ```
   */
  public async loadModel(
    id: string,
    options: {
      includeMetadata?: boolean;
      validateOnLoad?: boolean;
    } = {}
  ): Promise<VoxelModel> {
    // 实现逻辑
  }
}
```

### 10.2 README文档
每个主要模块都应该有README.md文件，包含：
- 模块概述和用途
- 安装和使用说明
- API文档链接
- 示例代码
- 贡献指南
- 许可证信息

## 11. 代码审查检查清单

### 11.1 提交前自检
- [ ] 代码通过TypeScript编译
- [ ] 所有测试通过
- [ ] 代码格式化完成 (Prettier)
- [ ] 代码检查通过 (ESLint)
- [ ] 性能关键代码有性能测试
- [ ] 公共API有完整文档
- [ ] 错误处理完整
- [ ] 内存泄漏检查完成

### 11.2 代码审查要点
- [ ] 代码逻辑正确性
- [ ] 类型安全性
- [ ] 性能影响评估
- [ ] 可维护性
- [ ] 测试覆盖率
- [ ] 文档完整性
- [ ] 安全性考虑
- [ ] 用户体验影响

## 12. 持续集成要求

### 12.1 自动化检查
- 每次提交自动运行：
  - TypeScript编译检查
  - ESLint代码检查
  - Prettier格式检查
  - 单元测试
  - 集成测试

### 12.2 性能监控
- 关键性能指标监控：
  - 渲染帧率 (≥60fps)
  - 内存使用量 (<512MB)
  - 启动时间 (<3秒)
  - 文件加载时间 (<1秒)

### 12.3 质量门禁
如果以下任一条件不满足，禁止合并代码：
- ❌ 测试覆盖率 < 80%
- ❌ 存在TypeScript错误
- ❌ 存在ESLint错误
- ❌ 性能测试不通过
- ❌ 代码审查未通过

---

## 总结

这份代码开发规范将指导整个项目的开发过程，确保：

1. **代码质量**: 通过严格的类型检查和测试要求
2. **性能优化**: 特别关注3D渲染和大数据处理性能
3. **可维护性**: 清晰的项目结构和文档要求
4. **团队协作**: 统一的编码风格和审查流程
5. **用户体验**: 所有技术决策都考虑最终用户体验

在后续开发中，我将严格遵循这些规范，确保交付高质量的代码。
