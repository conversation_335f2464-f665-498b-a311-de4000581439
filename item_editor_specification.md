# 物品编辑器规格文档

## 1. 概述

物品编辑器是体素游戏编辑器系统的核心模块之一，专门用于创建和管理游戏中的所有物品和装备。采用基于技能系统的属性设计理念，装备不直接提供属性加成，而是通过绑定技能来实现所有效果。支持复杂的物品分类、获取方式、交互机制，并与体素编辑器深度集成实现物品3D模型的自动/手动生成。

## 2. 核心功能

### 2.1 物品分类架构
```typescript
interface ItemClassificationSystem {
  // === 装备类 (Equipment) - 可穿戴装备 ===
  equipment: {
    weapons: {
      melee: ['剑', '斧', '锤', '匕首', '长矛', '双手剑']
      ranged: ['弓', '弩', '投掷武器', '魔法杖', '法球']
      special: ['双手武器', '组合武器', '变形武器']
    }
    
    armor: {
      helmet: ['头盔', '帽子', '面具', '头饰']
      chest: ['胸甲', '长袍', '夹克', '背心']
      legs: ['腿甲', '裤子', '裙子', '护腿']
      feet: ['靴子', '鞋子', '凉鞋', '战靴']
      gloves: ['手套', '护腕', '拳套']
    }
    
    accessories: {
      rings: ['戒指', '指环']
      necklaces: ['项链', '护身符', '吊坠']
      belts: ['腰带', '腰包', '束带']
      cloaks: ['斗篷', '披风', '外套']
    }
    
    tools: {
      gathering: ['镐', '斧', '锄头', '渔竿', '铲子']
      crafting: ['锤子', '钳子', '刻刀', '针线']
      utility: ['火把', '绳索', '梯子', '钩爪']
    }
  }
  
  // === 道具类 (Items) - 物品栏/背包物品 ===
  items: {
    // 可使用道具
    usableItems: {
      consumables: {
        food: ['面包', '肉类', '水果', '料理', '饮品']
        potions: ['生命药水', '法力药水', '增益药水', '解毒剂']
        seeds: ['小麦种子', '胡萝卜种子', '花种子', '树苗']
        ammunition: ['箭矢', '弩箭', '子弹', '飞刀']
      }
      
      functional: {
        books: ['技能书', '配方书', '日志', '地图', '说明书']
        keys: ['钥匙', '通行证', '令牌', '密钥卡']
        scrolls: ['传送卷轴', '技能卷轴', '召唤卷轴', '魔法卷轴']
        containers: ['背包', '箱子', '袋子', '工具箱', '药品包']
      }
      
      activatable: {
        teleportation: ['传送石', '回城卷轴', '定位符']
        summoning: ['召唤石', '宠物蛋', '召唤符']
        utility: ['照明弹', '信号弹', '陷阱', '探测器']
      }
    }
    
    // 不可使用道具
    nonUsableItems: {
      questItems: ['任务物品', '剧情道具', '收集品', '证据']
      materials: {
        basic: ['木材', '石头', '金属锭', '布料']
        rare: ['宝石', '魔法水晶', '稀有矿物', '龙鳞']
        crafting: ['皮革', '药草', '魔法粉末', '符文石']
      }
      currency: ['金币', '银币', '宝石币', '声望点', '荣誉点']
      collectibles: ['成就徽章', '纪念品', '装饰品', '收藏卡']
    }
  }
}
```

### 2.2 核心系统开关
```typescript
interface ItemSystemSwitches {
  // 容器系统开关
  containerSystem: {
    enabled: boolean                       // 容器系统总开关
    nestedContainers: boolean              // 嵌套容器 (背包套娃)
    containerTypes: {
      bags: boolean                        // 背包类容器
      chests: boolean                      // 箱子类容器
      pouches: boolean                     // 小袋类容器
      specialContainers: boolean           // 特殊容器 (工具箱、药品包等)
    }
  }
  
  // 绑定系统开关
  bindingSystem: {
    enabled: boolean                       // 绑定系统总开关
    bindingTypes: {
      pickupBind: boolean                  // 拾取绑定
      equipBind: boolean                   // 装备绑定
      useBind: boolean                     // 使用绑定
      questBind: boolean                   // 任务绑定 (不可丢弃/交易)
    }
    
    bindingRules: {
      allowDrop: boolean                   // 允许丢弃绑定物品
      allowDestroy: boolean                // 允许销毁绑定物品
      bindingIndicator: boolean            // 绑定状态显示
    }
  }
  
  // 耐久度系统开关
  durabilitySystem: {
    enabled: boolean                       // 耐久度系统总开关
    globalDurabilityDisplay: boolean       // 全局耐久度显示
    
    durabilityRules: {
      repairSystem: boolean                // 修理系统
      durabilityLoss: {
        onUse: boolean                     // 使用时损耗
        onDeath: boolean                   // 死亡时损耗
        overTime: boolean                  // 时间损耗
      }
      
      brokenItemBehavior: {
        becomeUseless: boolean             // 损坏后无法使用
        lowerEfficiency: boolean           // 损坏后效率降低
        autoDestroy: boolean               // 损坏后自动销毁
      }
    }
  }
  
  // 品质系统开关
  qualitySystem: {
    enabled: boolean                       // 品质系统总开关
    colorCoding: boolean                   // 颜色编码显示
    
    qualityLevels: {
      poor: { enabled: boolean, color: '#9d9d9d', name: '破损' }
      common: { enabled: boolean, color: '#ffffff', name: '普通' }
      uncommon: { enabled: boolean, color: '#1eff00', name: '优秀' }
      rare: { enabled: boolean, color: '#0070dd', name: '稀有' }
      epic: { enabled: boolean, color: '#a335ee', name: '史诗' }
      legendary: { enabled: boolean, color: '#ff8000', name: '传说' }
      artifact: { enabled: boolean, color: '#e6cc80', name: '神器' }
    }
    
    qualityEffects: {
      skillScaling: boolean                // 技能效果缩放
      appearanceEffects: boolean           // 外观效果
      namePrefix: boolean                  // 名称前缀
    }
  }
  
  // 高级装备系统开关
  advancedEquipmentSystems: {
    // 套装系统开关
    setSystem: {
      enabled: boolean
      setBonuses: {
        twoSetBonus: boolean               // 2件套效果
        fourSetBonus: boolean              // 4件套效果
        sixSetBonus: boolean               // 6件套效果
        fullSetBonus: boolean              // 全套效果
      }
    }
    
    // 强化系统开关
    enhancementSystem: {
      enabled: boolean
      enhancementTypes: {
        levelEnhancement: boolean          // 等级强化
        qualityUpgrade: boolean            // 品质升级
        skillEnhancement: boolean          // 技能强化
      }
    }
    
    // 附魔系统开关
    enchantmentSystem: {
      enabled: boolean
      enchantmentTypes: {
        weaponEnchantments: boolean        // 武器附魔
        armorEnchantments: boolean         // 护甲附魔
        accessoryEnchantments: boolean     // 饰品附魔
        toolEnchantments: boolean          // 工具附魔
      }
    }
    
    // 镶嵌系统开关
    socketSystem: {
      enabled: boolean
      socketTypes: {
        weaponSockets: boolean             // 武器插槽
        armorSockets: boolean              // 护甲插槽
        accessorySockets: boolean          // 饰品插槽
      }
    }
  }
}
```

### 2.3 物品获取系统
```typescript
interface ItemAcquisitionSystem {
  // 制作系统
  crafting: {
    synthesis: {                           // 合成
      basicCrafting: boolean               // 基础制作
      advancedCrafting: boolean            // 高级制作
      massCrafting: boolean                // 批量制作
    }
    
    decomposition: {                       // 分解
      materialRecovery: number             // 材料回收率 (0-100%)
      skillRequirement: string             // 需要的技能
      toolRequirement: string              // 需要的工具
    }
    
    planting: {                            // 种植
      growthTime: number                   // 生长时间
      yieldAmount: [number, number]        // 产出数量范围
      seasonalModifier: boolean            // 季节影响
    }
  }
  
  // 掉落系统
  drops: {
    monsterDrops: {
      dropRate: number                     // 掉落概率 (0-100%)
      qualityDistribution: {               // 品质分布
        common: number
        uncommon: number
        rare: number
        epic: number
        legendary: number
      }
      levelScaling: boolean                // 等级缩放
    }
    
    treasureChests: {
      chestTypes: ['木箱', '铁箱', '黄金箱', '魔法箱']
      guaranteedItems: string[]            // 保底物品
      bonusItems: string[]                 // 额外物品
      keyRequirement: string               // 钥匙需求
    }
  }
  
  // 商店系统
  shops: {
    npcShops: {
      stockRefreshTime: number             // 库存刷新时间
      priceFluctuation: boolean            // 价格波动
      reputationDiscount: boolean          // 声望折扣
    }
  }
  
  // 任务奖励
  questRewards: {
    fixedRewards: string[]                 // 固定奖励
    choiceRewards: string[]                // 选择奖励
    randomRewards: string[]                // 随机奖励
    reputationRewards: string[]            // 声望奖励
  }
  
  // 物品交互 (联机)
  playerInteraction: {
    dropToGround: {
      enabled: boolean                     // 允许丢到地上
      pickupByAnyone: boolean              // 任意玩家可拾取
      despawnTime: number                  // 消失时间 (秒)
      protectionTime: number               // 保护时间 (仅原主人可拾取)
    }
  }
}
```

## 3. 基于技能系统的物品属性设计

### 3.1 装备属性系统 (通过技能实现)
```typescript
interface EquipmentSkillBasedSystem {
  // === 装备专用属性 ===
  equipmentProperties: {
    // 装备槽位
    equipSlot: {
      slotType: 'weapon' | 'armor' | 'accessory' | 'tool'
      specificSlot: string                 // 具体槽位 (头盔、胸甲、戒指等)
      twoHanded: boolean                   // 是否双手装备
    }

    // 装备需求
    requirements: {
      levelRequirement: number             // 等级需求
      attributeRequirements: {             // 属性需求
        strength: number
        agility: number
        intelligence: number
        vitality: number
      }
      skillRequirements: string[]          // 技能需求
      questRequirements: string[]          // 任务需求
    }

    // ✅ 装备技能绑定系统 (替代直接属性)
    equipmentSkills: {
      // 被动装备技能 (装备时自动激活)
      passiveSkills: {
        attributeSkills: string[]          // 属性增加技能ID (如: "力量+10技能")
        resistanceSkills: string[]         // 抗性技能ID (如: "火焰抗性+25%技能")
        specialAbilitySkills: string[]     // 特殊能力技能ID (如: "夜视技能")
        combatSkills: string[]             // 战斗技能ID (如: "攻击力+15技能")
      }

      // 主动装备技能 (可手动释放)
      activeSkills: {
        equipmentActiveSkills: string[]    // 装备主动技能ID (如: "闪电链技能")
        cooldownSkills: string[]           // 有冷却的技能ID
        chargeSkills: string[]             // 有使用次数的技能ID
      }

      // 触发装备技能 (条件触发)
      triggerSkills: {
        onAttackSkills: string[]           // 攻击时触发的技能ID
        onDefendSkills: string[]           // 防御时触发的技能ID
        onCriticalSkills: string[]         // 暴击时触发的技能ID
        onLowHealthSkills: string[]        // 低血量时触发的技能ID
      }

      // 套装技能 (套装件数达到时激活)
      setSkills: {
        twoSetSkills: string[]             // 2件套技能ID
        fourSetSkills: string[]            // 4件套技能ID
        sixSetSkills: string[]             // 6件套技能ID
        fullSetSkills: string[]            // 全套技能ID
      }
    }

    // 套装信息
    setInfo: {
      setId: string                        // 套装ID
      setPiece: number                     // 套装件数
      setName: string                      // 套装名称
    }

    // 强化信息 (强化影响技能效果)
    enhancement: {
      enhancementLevel: number             // 强化等级
      skillEnhancementBonus: number        // 技能效果增强百分比
      maxEnhancementLevel: number          // 最大强化等级
    }

    // 附魔信息 (附魔添加额外技能)
    enchantments: {
      enchantmentSlots: number             // 附魔槽位数量
      enchantmentSkills: string[]          // 附魔技能ID列表
      maxEnchantments: number              // 最大附魔数量
    }

    // 镶嵌信息 (宝石提供技能)
    sockets: {
      socketCount: number                  // 插槽数量
      socketTypes: string[]                // 插槽类型
      socketedGemSkills: string[]          // 镶嵌宝石提供的技能ID列表
    }
  }
}
```

### 3.2 道具属性系统
```typescript
interface ItemPropertiesSystem {
  // === 道具专用属性 ===
  itemProperties: {
    // 使用属性
    usageProperties: {
      usable: boolean                      // 是否可使用
      useType: 'instant' | 'channeled' | 'toggle' // 使用类型
      targetType: 'self' | 'target' | 'ground' | 'area' // 目标类型
      range: number                        // 使用范围
      cooldown: number                     // 冷却时间
      charges: number                      // 使用次数 (-1为无限)
      consumeOnUse: boolean                // 使用后是否消耗
    }

    // 容器属性
    containerProperties: {
      isContainer: boolean                 // 是否为容器
      containerType: 'bag' | 'chest' | 'pouch' | 'special'
      capacity: number                     // 容量 (格子数)
      allowedItemTypes: string[]           // 允许存放的物品类型
      nestedContainers: boolean            // 是否允许嵌套容器
      accessPermissions: 'owner' | 'party' | 'guild' | 'anyone' // 访问权限
    }

    // 特殊属性
    specialProperties: {
      questItem: boolean                   // 是否为任务物品
      keyItem: boolean                     // 是否为关键物品
      craftingMaterial: boolean            // 是否为制作材料
      currency: boolean                    // 是否为货币

      // 种子属性
      seedProperties: {
        isSeed: boolean                    // 是否为种子
        growthTime: number                 // 生长时间
        harvestYield: [number, number]     // 收获数量范围
        seasonRequirement: string          // 季节需求
        soilRequirement: string            // 土壤需求
      }

      // 食物属性
      foodProperties: {
        isFood: boolean                    // 是否为食物
        hungerRestore: number              // 饥饿恢复
        thirstRestore: number              // 口渴恢复
        healthRestore: number              // 生命恢复
        buffEffects: string[]              // 增益效果
        spoilTime: number                  // 腐败时间 (0为不腐败)
      }

      // 药水属性
      potionProperties: {
        isPotion: boolean                  // 是否为药水
        effectType: 'instant' | 'overtime' // 效果类型
        effectDuration: number             // 效果持续时间
        effectStrength: number             // 效果强度
        stackable: boolean                 // 效果是否可叠加
      }
    }

    // 道具技能
    itemSkills: {
      useSkill: string                     // 使用时释放的技能ID
      passiveSkills: string[]              // 持有时的被动技能
      triggerSkills: {                     // 触发技能
        onPickup: string                   // 拾取时触发
        onDrop: string                     // 丢弃时触发
        onDestroy: string                  // 销毁时触发
      }
    }
  }
}
```

### 3.3 与技能编辑器的集成
```typescript
interface SkillEditorIntegration {
  // 在技能编辑器中新增的装备/道具技能类型
  itemEquipmentSkillTypes: {
    // === 装备被动技能 ===
    equipmentPassiveSkills: {
      // 属性增强技能
      attributeEnhancement: {
        examples: [
          '力量+10', '敏捷+15', '智力+20', '体力+25',
          '力量+15%', '敏捷+20%', '智力+10%', '体力+30%'
        ]
        mechanics: {
          bonusType: 'flat' | 'percentage'   // 固定值或百分比
          stackable: boolean                 // 是否可叠加
          scalingWithLevel: boolean          // 是否随等级缩放
        }
      }

      // 战斗属性技能
      combatEnhancement: {
        examples: [
          '攻击力+50', '防御力+30', '命中率+10%', '暴击率+5%',
          '攻击速度+15%', '移动速度+20%', '法术强度+40'
        ]
        mechanics: {
          bonusType: 'flat' | 'percentage'
          combatOnly: boolean                // 仅战斗时生效
          weaponSpecific: boolean            // 特定武器类型生效
        }
      }

      // 抗性技能
      resistanceSkills: {
        examples: [
          '物理抗性+25%', '魔法抗性+20%', '火焰抗性+30%',
          '冰霜抗性+25%', '毒素抗性+40%', '全抗性+10%'
        ]
        mechanics: {
          resistanceType: string             // 抗性类型
          maxResistance: number              // 最大抗性值
          stackingRules: string              // 叠加规则
        }
      }

      // 特殊能力技能
      specialAbilities: {
        examples: [
          '夜视', '水下呼吸', '飞行', '隐身', '传送',
          '自动拾取', '经验加成+50%', '掉落率+25%'
        ]
        mechanics: {
          abilityType: string                // 能力类型
          duration: number                   // 持续时间 (-1为永久)
          toggleable: boolean                // 是否可开关
        }
      }
    }

    // === 装备主动技能 ===
    equipmentActiveSkills: {
      // 释放型技能 (基于现有的主动技能机制)
      castableSkills: {
        examples: [
          '闪电链 (弹射类)', '治疗术 (单体锁定类)', '火球术 (单体矢类)',
          '传送术 (位移类)', '召唤骷髅 (召唤类)', '火墙术 (放置类)'
        ]
        mechanics: {
          basedOnExistingSkills: boolean     // 基于现有技能机制
          cooldown: number                   // 冷却时间
          manaCost: number                   // 法力消耗
          charges: number                    // 使用次数 (-1为无限)
        }
      }

      // 增益型技能
      buffSkills: {
        examples: [
          '狂暴 (攻击力+100%, 持续30秒)', '护盾 (吸收500伤害)',
          '急速 (移动速度+50%, 持续20秒)', '隐身 (持续10秒)'
        ]
        mechanics: {
          buffType: string                   // 增益类型
          duration: number                   // 持续时间
          stackable: boolean                 // 是否可叠加
        }
      }
    }

    // === 道具使用技能 ===
    itemUseSkills: {
      // 消耗品技能
      consumableSkills: {
        examples: [
          '恢复生命500点', '恢复法力300点', '移除所有负面状态',
          '临时增加力量+50 (持续5分钟)', '瞬间传送到城镇'
        ]
        mechanics: {
          instantEffect: boolean             // 即时效果
          overTimeEffect: boolean            // 持续效果
          consumeItem: boolean               // 是否消耗物品
        }
      }

      // 功能道具技能
      functionalSkills: {
        examples: [
          '打开特定门锁', '激活传送门', '召唤商人',
          '创建临时营地', '标记地图位置', '发送消息'
        ]
        mechanics: {
          targetRequired: boolean            // 是否需要目标
          environmentInteraction: boolean    // 环境交互
          multiUse: boolean                  // 是否可重复使用
        }
      }
    }

    // === 触发技能 ===
    triggerSkills: {
      // 条件触发技能
      conditionalTriggers: {
        examples: [
          '攻击时10%概率释放闪电', '受伤时自动治疗',
          '暴击时恢复法力', '击杀敌人时获得增益'
        ]
        mechanics: {
          triggerCondition: string           // 触发条件
          triggerChance: number              // 触发概率
          cooldownBetweenTriggers: number    // 触发间隔
        }
      }

      // 状态触发技能
      statusTriggers: {
        examples: [
          '生命值低于30%时激活护盾', '法力值低于20%时恢复法力',
          '中毒时自动解毒', '被冰冻时自动解冻'
        ]
        mechanics: {
          statusThreshold: number            // 状态阈值
          autoActivation: boolean            // 自动激活
          statusType: string                 // 状态类型
        }
      }
    }
  }
}
```

## 4. 脚本库系统

### 4.1 脚本分类标识系统
```typescript
interface ScriptLibrarySystem {
  // 脚本分类标识
  scriptCategories: {
    itemScripts: {
      identifier: '##物品脚本##'
      description: '专用于物品编辑器的脚本'
      availableIn: ['item_editor']
    }

    skillScripts: {
      identifier: '##技能脚本##'
      description: '专用于技能编辑器的脚本'
      availableIn: ['skill_editor']
    }

    lifeSystemScripts: {
      identifier: '##生活系统脚本##'
      description: '专用于生活系统编辑器的脚本'
      availableIn: ['life_system_editor']
    }

    unitScripts: {
      identifier: '##单位脚本##'
      description: '专用于单位编辑器的脚本'
      availableIn: ['unit_editor']
    }

    globalScripts: {
      identifier: '##全局脚本##'
      description: '可在所有编辑器中使用的脚本'
      availableIn: ['all_editors']
    }
  }

  // 物品脚本预设库
  itemScriptPresets: {
    // 装备脚本
    equipmentEffects: {
      passiveEffects: [
        '##物品脚本##\n// 被动属性加成\nfunction passiveAttributeBonus(attribute, amount) {\n  player.addAttribute(attribute, amount);\n}'
        '##物品脚本##\n// 被动技能效果\nfunction passiveSkillEffect(skillId) {\n  player.addPassiveSkill(skillId);\n}'
        '##物品脚本##\n// 被动抗性加成\nfunction passiveResistance(resistanceType, amount) {\n  player.addResistance(resistanceType, amount);\n}'
      ]

      triggerEffects: [
        '##物品脚本##\n// 攻击触发\nfunction onAttackTrigger(chance, effect) {\n  if (Math.random() < chance) {\n    effect.execute();\n  }\n}'
        '##物品脚本##\n// 受伤触发\nfunction onDamageTrigger(chance, effect) {\n  if (Math.random() < chance) {\n    effect.execute();\n  }\n}'
        '##物品脚本##\n// 暴击触发\nfunction onCriticalTrigger(effect) {\n  effect.execute();\n}'
      ]

      activeEffects: [
        '##物品脚本##\n// 装备主动技能\nfunction equipmentActiveSkill(skillId, cooldown) {\n  if (canUseSkill(skillId, cooldown)) {\n    useSkill(skillId);\n  }\n}'
        '##物品脚本##\n// 装备增益技能\nfunction equipmentBuffSkill(buffType, duration, strength) {\n  player.addBuff(buffType, duration, strength);\n}'
      ]
    }

    // 消耗品脚本
    consumableEffects: {
      healingEffects: [
        '##物品脚本##\n// 即时治疗\nfunction instantHeal(amount) {\n  player.health += amount;\n  if (player.health > player.maxHealth) {\n    player.health = player.maxHealth;\n  }\n}'
        '##物品脚本##\n// 持续治疗\nfunction healOverTime(amount, duration) {\n  player.addBuff("heal_over_time", duration, amount);\n}'
        '##物品脚本##\n// 法力恢复\nfunction restoreMana(amount) {\n  player.mana += amount;\n  if (player.mana > player.maxMana) {\n    player.mana = player.maxMana;\n  }\n}'
      ]

      buffEffects: [
        '##物品脚本##\n// 属性增益\nfunction attributeBuff(attribute, amount, duration) {\n  player.addBuff(attribute + "_buff", duration, amount);\n}'
        '##物品脚本##\n// 速度增益\nfunction speedBoost(multiplier, duration) {\n  player.addBuff("speed_boost", duration, multiplier);\n}'
        '##物品脚本##\n// 抗性增益\nfunction resistanceBuff(resistanceType, amount, duration) {\n  player.addBuff(resistanceType + "_resistance", duration, amount);\n}'
      ]

      utilityEffects: [
        '##物品脚本##\n// 传送效果\nfunction teleport(x, y, z) {\n  player.teleportTo(x, y, z);\n}'
        '##物品脚本##\n// 照明效果\nfunction createLight(radius, duration) {\n  world.createLight(player.position, radius, duration);\n}'
        '##物品脚本##\n// 解毒效果\nfunction removePoison() {\n  player.removeDebuff("poison");\n}'
      ]
    }

    // 功能道具脚本
    functionalEffects: {
      interactionEffects: [
        '##物品脚本##\n// 物品组合\nfunction combineItems(item1, item2, result) {\n  if (player.hasItem(item1) && player.hasItem(item2)) {\n    player.removeItem(item1);\n    player.removeItem(item2);\n    player.addItem(result);\n  }\n}'
        '##物品脚本##\n// 物品转换\nfunction transformItem(sourceItem, targetItem) {\n  if (player.hasItem(sourceItem)) {\n    player.removeItem(sourceItem);\n    player.addItem(targetItem);\n  }\n}'
      ]

      environmentEffects: [
        '##物品脚本##\n// 环境交互\nfunction environmentInteraction(blockType, effect) {\n  const targetBlock = player.getTargetBlock();\n  if (targetBlock.type === blockType) {\n    effect.execute(targetBlock);\n  }\n}'
        '##物品脚本##\n// 门锁交互\nfunction unlockDoor(keyId, doorId) {\n  if (player.hasItem(keyId)) {\n    world.unlockDoor(doorId);\n    player.removeItem(keyId);\n  }\n}'
      ]

      summoning: [
        '##物品脚本##\n// 召唤生物\nfunction summonCreature(creatureId, duration) {\n  const summon = world.summonCreature(creatureId, player.position);\n  summon.setOwner(player);\n  summon.setDuration(duration);\n}'
        '##物品脚本##\n// 召唤商人\nfunction summonMerchant(merchantId, duration) {\n  const merchant = world.summonMerchant(merchantId, player.position);\n  merchant.setDuration(duration);\n}'
      ]
    }

    // 容器脚本
    containerEffects: [
      '##物品脚本##\n// 背包扩容\nfunction expandInventory(slots) {\n  player.inventory.addSlots(slots);\n}'
      '##物品脚本##\n// 特殊容器访问\nfunction accessSpecialContainer(containerId) {\n  const container = world.getContainer(containerId);\n  player.openContainer(container);\n}'
      '##物品脚本##\n// 容器权限检查\nfunction checkContainerAccess(containerId, requiredPermission) {\n  return player.hasPermission(requiredPermission);\n}'
    ]
  }
}
```

## 5. 用户界面设计

### 5.1 物品创建向导 (参考WE物品编辑器)
```typescript
interface ItemCreationWizard {
  // 第1步: 物品类型选择
  step1_ItemTypeSelection: {
    title: '选择物品类型'
    categories: [
      {
        id: 'weapon'
        name: '武器'
        description: '用于战斗的装备'
        subcategories: ['近战武器', '远程武器', '魔法武器', '特殊武器']
        icon: 'weapon_icon'
      }
      {
        id: 'armor'
        name: '护甲'
        description: '提供防护的装备'
        subcategories: ['头部护甲', '身体护甲', '腿部护甲', '脚部护甲', '手部护甲']
        icon: 'armor_icon'
      }
      {
        id: 'accessory'
        name: '饰品'
        description: '提供特殊效果的装备'
        subcategories: ['戒指', '项链', '腰带', '斗篷']
        icon: 'accessory_icon'
      }
      {
        id: 'tool'
        name: '工具'
        description: '用于采集和制作的装备'
        subcategories: ['采集工具', '制作工具', '实用工具']
        icon: 'tool_icon'
      }
      {
        id: 'consumable'
        name: '消耗品'
        description: '可使用的道具'
        subcategories: ['食物', '药水', '种子', '弹药']
        icon: 'consumable_icon'
      }
      {
        id: 'functional'
        name: '功能道具'
        description: '具有特殊功能的道具'
        subcategories: ['书籍', '钥匙', '卷轴', '容器']
        icon: 'functional_icon'
      }
      {
        id: 'material'
        name: '材料'
        description: '用于制作的材料'
        subcategories: ['基础材料', '稀有材料', '制作材料']
        icon: 'material_icon'
      }
      {
        id: 'special'
        name: '特殊物品'
        description: '任务物品和收藏品'
        subcategories: ['任务物品', '货币', '收藏品']
        icon: 'special_icon'
      }
    ]
  }

  // 第2步: 基础属性配置 (参考WE)
  step2_BasicProperties: {
    title: '配置基础属性'
    sections: [
      {
        name: '基本信息'
        fields: [
          { name: '物品ID', type: 'string', required: true }
          { name: '物品名称', type: 'string', required: true }
          { name: '物品描述', type: 'text', required: false }
          { name: '图标', type: 'image_selector', required: true }
          { name: '3D模型', type: 'voxel_model_selector', required: true }
        ]
      }
      {
        name: '分类信息'
        fields: [
          { name: '主分类', type: 'enum', options: ['equipment', 'item'] }
          { name: '子分类', type: 'string', required: true }
          { name: '物品类型', type: 'string', required: true }
        ]
      }
      {
        name: '数值属性'
        fields: [
          { name: '物品等级', type: 'number', min: 0, max: 1000, default: 0 }
          { name: '品质', type: 'enum', options: ['poor', 'common', 'uncommon', 'rare', 'epic', 'legendary', 'artifact'] }
          { name: '基础价值', type: 'number', min: 0, default: 1 }
          { name: '重量', type: 'number', min: 0, default: 0 }
        ]
      }
      {
        name: '堆叠设置'
        fields: [
          { name: '可堆叠', type: 'boolean', default: false }
          { name: '最大堆叠数', type: 'number', min: 1, max: 9999, default: 1 }
        ]
      }
    ]
  }

  // 第3步: 系统开关配置
  step3_SystemSwitches: {
    title: '配置系统开关'
    switches: [
      {
        name: '耐久度系统'
        enabled: boolean
        config: [
          { name: '最大耐久度', type: 'number', min: 0, default: 0 }
          { name: '修理费用', type: 'number', min: 0, default: 0 }
          { name: '损坏行为', type: 'enum', options: ['useless', 'lower_efficiency', 'auto_destroy'] }
        ]
      }
      {
        name: '绑定系统'
        enabled: boolean
        config: [
          { name: '绑定类型', type: 'enum', options: ['none', 'pickup', 'equip', 'use', 'quest'] }
          { name: '可丢弃', type: 'boolean', default: true }
          { name: '可交易', type: 'boolean', default: true }
          { name: '可销毁', type: 'boolean', default: true }
        ]
      }
      {
        name: '容器系统'
        enabled: boolean
        config: [
          { name: '容器类型', type: 'enum', options: ['bag', 'chest', 'pouch', 'special'] }
          { name: '容量', type: 'number', min: 1, max: 100, default: 10 }
          { name: '允许嵌套', type: 'boolean', default: false }
          { name: '访问权限', type: 'enum', options: ['owner', 'party', 'guild', 'anyone'] }
        ]
      }
    ]
  }

  // 第4步: 技能绑定配置
  step4_SkillBinding: {
    title: '配置技能绑定'
    equipmentSkillBinding: {
      sections: [
        {
          name: '被动技能 (装备时自动激活)'
          categories: [
            {
              name: '属性增强'
              availableSkills: string[]      // 从技能编辑器获取的属性增强技能列表
              selectedSkills: string[]       // 已选择的技能
              maxSkills: 5                   // 最大技能数量
            }
            {
              name: '战斗增强'
              availableSkills: string[]
              selectedSkills: string[]
              maxSkills: 3
            }
            {
              name: '抗性技能'
              availableSkills: string[]
              selectedSkills: string[]
              maxSkills: 3
            }
            {
              name: '特殊能力'
              availableSkills: string[]
              selectedSkills: string[]
              maxSkills: 2
            }
          ]
        }
        {
          name: '主动技能 (可手动释放)'
          skillSlots: [
            {
              slotId: 1
              skillId: string                // 绑定的技能ID
              activationKey: string          // 激活按键
              cooldown: number               // 冷却时间
              charges: number                // 使用次数
            }
            // ... 更多技能槽位
          ]
          maxActiveSkills: 3               // 最大主动技能数量
        }
        {
          name: '触发技能 (条件触发)'
          triggers: [
            {
              triggerType: 'onAttack' | 'onDefend' | 'onCritical' | 'onLowHealth'
              skillId: string                // 触发的技能ID
              triggerChance: number          // 触发概率
              cooldown: number               // 触发冷却
            }
            // ... 更多触发器
          ]
        }
        {
          name: '套装技能 (套装件数激活)'
          setRequirements: [
            {
              requiredPieces: 2
              setSkills: string[]            // 2件套技能
            }
            {
              requiredPieces: 4
              setSkills: string[]            // 4件套技能
            }
            // ... 更多套装需求
          ]
        }
      ]
    }

    itemSkillBinding: {
      sections: [
        {
          name: '使用技能 (使用物品时释放)'
          useSkill: {
            skillId: string                  // 使用技能ID
            targetType: 'self' | 'target' | 'ground' | 'area'
            range: number                    // 使用范围
            cooldown: number                 // 冷却时间
            consumeOnUse: boolean            // 使用后是否消耗
          }
        }
        {
          name: '被动技能 (持有时生效)'
          passiveSkills: string[]           // 被动技能ID列表
          requiresEquipped: boolean          // 是否需要装备才生效
        }
        {
          name: '触发技能'
          triggers: [
            {
              triggerEvent: 'onPickup' | 'onDrop' | 'onDestroy' | 'onCombine'
              skillId: string                // 触发的技能ID
              triggerOnce: boolean           // 是否只触发一次
            }
          ]
        }
      ]
    }
  }

  // 第5步: 脚本和特效配置
  step5_ScriptsAndEffects: {
    title: '配置脚本和特效'
    sections: [
      {
        name: '脚本绑定'
        scripts: [
          { name: '使用脚本', type: 'script_selector', filter: '##物品脚本##' }
          { name: '装备脚本', type: 'script_selector', filter: '##物品脚本##' }
          { name: '被动脚本', type: 'script_selector', filter: '##物品脚本##' }
          { name: '触发脚本', type: 'script_selector', filter: '##物品脚本##' }
        ]
      }
      {
        name: '视觉效果'
        effects: [
          { name: '粒子特效', type: 'effect_selector', source: 'effect_editor' }
          { name: '发光效果', type: 'boolean', default: false }
          { name: '自定义颜色', type: 'color_picker', default: '#ffffff' }
          { name: '模型缩放', type: 'number', min: 0.1, max: 5.0, default: 1.0 }
        ]
      }
      {
        name: '音效绑定'
        sounds: [
          { name: '使用音效', type: 'audio_selector', source: 'audio_editor' }
          { name: '装备音效', type: 'audio_selector', source: 'audio_editor' }
          { name: '环境音效', type: 'audio_selector', source: 'audio_editor' }
        ]
      }
    ]
  }

  // 第6步: 预览和测试
  step6_PreviewAndTest: {
    title: '预览和测试'
    previewModes: [
      {
        name: '3D模型预览'
        description: '查看物品的3D模型和外观效果'
        features: ['模型旋转', '缩放查看', '材质预览', '特效预览']
      }
      {
        name: '属性预览'
        description: '查看物品的完整属性面板'
        features: ['工具提示预览', '属性列表', '技能效果', '品质显示']
      }
      {
        name: '功能测试'
        description: '测试物品的使用功能和效果'
        features: ['使用测试', '技能测试', '脚本测试', '触发测试']
      }
      {
        name: '兼容性检查'
        description: '检查与其他系统的兼容性'
        features: ['技能兼容性', '模型兼容性', '脚本兼容性', '系统兼容性']
      }
    ]
  }
}
```

## 6. 与体素编辑器的集成

### 6.1 物品模型生成系统
```typescript
interface VoxelEditorIntegration {
  // 物品模型生成
  itemModelGeneration: {
    // 自动生成选项
    autoGeneration: {
      enabled: boolean
      generationRules: {
        weaponModels: {
          swordGeneration: boolean         // 剑类自动生成
          axeGeneration: boolean           // 斧类自动生成
          bowGeneration: boolean           // 弓类自动生成
          staffGeneration: boolean         // 法杖自动生成
          hammerGeneration: boolean        // 锤类自动生成
          daggerGeneration: boolean        // 匕首自动生成
        }

        armorModels: {
          helmetGeneration: boolean        // 头盔自动生成
          chestplateGeneration: boolean    // 胸甲自动生成
          leggingsGeneration: boolean      // 腿甲自动生成
          bootsGeneration: boolean         // 靴子自动生成
          glovesGeneration: boolean        // 手套自动生成
        }

        toolModels: {
          pickaxeGeneration: boolean       // 镐子自动生成
          axeGeneration: boolean           // 斧子自动生成
          hoeGeneration: boolean           // 锄头自动生成
          shovelGeneration: boolean        // 铲子自动生成
          hammerGeneration: boolean        // 锤子自动生成
        }

        itemModels: {
          potionGeneration: boolean        // 药水自动生成
          foodGeneration: boolean          // 食物自动生成
          gemGeneration: boolean           // 宝石自动生成
          bookGeneration: boolean          // 书籍自动生成
          scrollGeneration: boolean        // 卷轴自动生成
          keyGeneration: boolean           // 钥匙自动生成
        }

        containerModels: {
          bagGeneration: boolean           // 背包自动生成
          chestGeneration: boolean         // 箱子自动生成
          pouchGeneration: boolean         // 小袋自动生成
        }
      }
    }

    // 手动创建选项
    manualCreation: {
      enabled: boolean
      creationModes: {
        fromScratch: boolean               // 从零开始创建
        fromTemplate: boolean              // 从模板创建
        fromExisting: boolean              // 从现有模型修改
        fromPreset: boolean                // 从预设形状创建
      }

      itemSpecificTools: {
        weaponTools: {
          bladeShaping: boolean            // 刀刃塑形工具
          handleDesign: boolean            // 手柄设计工具
          guardCreation: boolean           // 护手创建工具
          pommelDesign: boolean            // 剑柄设计工具
        }

        armorTools: {
          plateShaping: boolean            // 板甲塑形工具
          chainmailPattern: boolean        // 锁甲图案工具
          leatherTexturing: boolean        // 皮革纹理工具
          decorativeElements: boolean      // 装饰元素工具
        }

        accessoryTools: {
          ringShaping: boolean             // 戒指塑形工具
          gemSetting: boolean              // 宝石镶嵌工具
          chainDesign: boolean             // 链条设计工具
          pendantCreation: boolean         // 吊坠创建工具
        }

        containerTools: {
          volumeDesign: boolean            // 容积设计工具
          lidMechanism: boolean            // 盖子机制工具
          handlePlacement: boolean         // 把手放置工具
          lockDesign: boolean              // 锁具设计工具
        }
      }
    }

    // 模型属性映射
    modelPropertyMapping: {
      sizeMapping: {
        [itemType: string]: {
          defaultSize: [number, number, number] // 默认尺寸
          minSize: [number, number, number]     // 最小尺寸
          maxSize: [number, number, number]     // 最大尺寸
          recommendedSize: [number, number, number] // 推荐尺寸
        }
      }

      materialMapping: {
        [itemType: string]: {
          defaultMaterial: string          // 默认材质
          allowedMaterials: string[]       // 允许的材质
          materialEffects: boolean         // 材质特效
          qualityMaterials: {              // 品质对应材质
            poor: string[]
            common: string[]
            uncommon: string[]
            rare: string[]
            epic: string[]
            legendary: string[]
          }
        }
      }

      detailMapping: {
        [itemType: string]: {
          requiredDetails: string[]        // 必需的细节
          optionalDetails: string[]        // 可选的细节
          detailComplexity: 'low' | 'medium' | 'high' // 细节复杂度
          qualityDetailLevel: {            // 品质对应细节等级
            poor: 'low'
            common: 'low'
            uncommon: 'medium'
            rare: 'medium'
            epic: 'high'
            legendary: 'high'
          }
        }
      }
    }
  }

  // 模型验证和优化
  modelValidation: {
    validationRules: {
      sizeValidation: {
        enabled: boolean
        checkMinSize: boolean              // 检查最小尺寸
        checkMaxSize: boolean              // 检查最大尺寸
        checkProportions: boolean          // 检查比例
      }

      complexityValidation: {
        enabled: boolean
        maxVoxelCount: number              // 最大体素数量
        maxDetailLevel: number             // 最大细节等级
        performanceThreshold: number       // 性能阈值
      }

      functionalValidation: {
        enabled: boolean
        checkUsability: boolean            // 检查可用性
        checkVisibility: boolean           // 检查可见性
        checkRecognizability: boolean      // 检查可识别性
      }
    }

    optimizationSuggestions: {
      lodGeneration: {
        enabled: boolean                   // LOD生成建议
        lodLevels: number                  // LOD等级数
        reductionRatio: number             // 减少比例
      }

      textureOptimization: {
        enabled: boolean                   // 纹理优化建议
        textureResolution: number          // 纹理分辨率
        compressionLevel: number           // 压缩等级
      }

      performanceOptimization: {
        enabled: boolean                   // 性能优化建议
        polygonReduction: boolean          // 多边形减少建议
        cullingOptimization: boolean       // 剔除优化建议
        batchingOptimization: boolean      // 批处理优化建议
      }
    }
  }

  // 体素编辑器中的物品选项
  voxelEditorItemOptions: {
    // 物品创建模式
    itemCreationMode: {
      enabled: boolean                     // 物品创建模式开关
      itemTypeSelector: {
        weaponMode: boolean                // 武器创建模式
        armorMode: boolean                 // 护甲创建模式
        toolMode: boolean                  // 工具创建模式
        accessoryMode: boolean             // 饰品创建模式
        consumableMode: boolean            // 消耗品创建模式
        containerMode: boolean             // 容器创建模式
      }
    }

    // 自动生成选项
    autoGenerationOptions: {
      generateBasicShape: boolean          // 生成基础形状
      generateDetails: boolean             // 生成细节
      generateTextures: boolean            // 生成纹理
      generateVariations: boolean          // 生成变体
    }

    // 手动编辑增强
    manualEditingEnhancements: {
      itemSpecificBrushes: boolean         // 物品专用笔刷
      symmetryTools: boolean               // 对称工具
      proportionGuides: boolean            // 比例指南
      referenceOverlays: boolean           // 参考覆盖层
    }

    // 实时预览
    realTimePreview: {
      itemPreview: boolean                 // 物品预览
      qualityPreview: boolean              // 品质预览
      effectPreview: boolean               // 特效预览
      scalePreview: boolean                // 缩放预览
    }
  }
}
```

---

## 总结

物品编辑器作为体素游戏编辑器系统的核心模块，提供了：

### 核心功能
- **完整的物品分类系统**: 装备类和道具类的详细分类，支持复杂的物品类型
- **基于技能的属性系统**: 装备不直接提供属性，而是通过绑定技能实现所有效果
- **灵活的系统开关**: 容器、绑定、耐久度、品质等系统都可独立开关
- **多样的获取方式**: 制作、掉落、商店、任务、玩家交互等完整的获取体系
- **智能脚本库**: ##物品脚本##标识符系统，专用脚本库支持

### 与技能编辑器的深度集成
- **装备技能系统**: 被动技能、主动技能、触发技能、套装技能的完整支持
- **道具技能系统**: 使用技能、被动技能、触发技能的灵活绑定
- **技能类型扩展**: 在技能编辑器中新增装备/道具专用技能类型
- **统一的技能机制**: 所有物品效果都通过技能系统实现，保持一致性

### 与体素编辑器的集成
- **自动模型生成**: 支持各种物品类型的自动3D模型生成
- **手动创建工具**: 物品专用的体素编辑工具和模板
- **智能验证优化**: 模型尺寸、复杂度、性能的自动检查和优化建议
- **实时预览**: 在体素编辑器中实时预览物品效果

### 用户体验优化
- **6步创建向导**: 从物品类型选择到预览测试的完整流程
- **参考WE设计**: 借鉴魔兽争霸3物品编辑器的成熟设计理念
- **可视化界面**: 直观的属性配置和技能绑定界面
- **智能推荐**: 基于物品类型的智能配置推荐

### 系统特色
- **技能驱动**: 所有物品效果都通过技能系统实现，统一且灵活
- **模块化设计**: 所有复杂功能都可独立开关，适应不同游戏需求
- **脚本支持**: 完整的脚本库系统，支持复杂的自定义效果
- **品质系统**: 完整的品质等级和颜色编码系统
- **容器嵌套**: 支持背包套娃等复杂容器系统

### 适用场景
- **ARPG游戏**: 完整的装备和道具系统
- **生存游戏**: 工具、食物、材料的完整支持
- **建造游戏**: 建造工具和材料的专门支持
- **RPG游戏**: 复杂的装备套装和技能系统
- **多人游戏**: 完整的物品交互和绑定机制

**注意**:
- 装备属性完全通过技能系统实现，不直接提供数值加成
- 脚本库使用##物品脚本##标识符，确保脚本的专用性和安全性
- 与体素编辑器深度集成，支持物品3D模型的自动/手动生成
- 所有复杂系统都可独立开关，支持从简单到复杂的各种游戏需求
