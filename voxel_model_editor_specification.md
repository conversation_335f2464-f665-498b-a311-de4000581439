# 体素模型编辑器规格文档

## 1. 概述

体素模型编辑器是一个专门用于创建和编辑3D体素模型的工具，为动画编辑器和单位编辑器提供模型资源。采用直观的体素编辑方式，支持从简单几何体到复杂生物模型的创建。

## 2. 核心功能

### 2.1 体素编辑工具
```typescript
interface VoxelEditingTools {
  // === 基础编辑工具 ===
  basicTools: {
    // 体素操作
    addVoxel: { brushSize: number, brushShape: 'cube' | 'sphere' | 'cylinder' }
    removeVoxel: { brushSize: number, brushShape: 'cube' | 'sphere' | 'cylinder' }
    paintVoxel: { color: Color, material: MaterialType }
    selectVoxel: { selectionMode: 'single' | 'box' | 'lasso' | 'magic_wand' }
    
    // 变换工具
    move: { axis: 'x' | 'y' | 'z' | 'free', snap: boolean }
    rotate: { axis: 'x' | 'y' | 'z' | 'free', angle: number }
    scale: { uniform: boolean, axis: 'x' | 'y' | 'z' | 'free' }
    mirror: { axis: 'x' | 'y' | 'z', keepOriginal: boolean }
    
    // 对称编辑
    symmetry: {
      enabled: boolean
      axis: 'x' | 'y' | 'z' | 'xy' | 'xz' | 'yz' | 'xyz'
      centerPoint: [number, number, number]
    }
  }
  
  // === 高级编辑工具 ===
  advancedTools: {
    // 形状生成器
    shapeGenerator: {
      primitives: {
        cube: { size: [number, number, number] }
        sphere: { radius: number, hollow: boolean }
        cylinder: { radius: number, height: number, hollow: boolean }
        cone: { radius: number, height: number, hollow: boolean }
        torus: { majorRadius: number, minorRadius: number }
      }
      
      // 有机形状
      organicShapes: {
        blob: { size: number, roughness: number, seed: number }
        tree: { height: number, branches: number, leafDensity: number }
        rock: { size: number, roughness: number, layering: boolean }
      }
    }
    
    // 噪声工具
    noiseTools: {
      perlinNoise: { scale: number, octaves: number, persistence: number }
      simplexNoise: { scale: number, amplitude: number }
      voronoiNoise: { cellSize: number, randomness: number }
      
      // 噪声应用
      applyTo: 'height' | 'density' | 'color' | 'material'
      strength: number
      mask: boolean
    }
    
    // 雕刻工具
    sculptingTools: {
      smooth: { strength: number, radius: number }
      roughen: { strength: number, radius: number }
      inflate: { strength: number, radius: number }
      deflate: { strength: number, radius: number }
      pinch: { strength: number, radius: number }
    }
  }
}
```

### 2.2 材质和外观系统
```typescript
interface MaterialSystem {
  // === 材质类型 ===
  materialTypes: {
    // 有机材质
    organic: {
      flesh: { color: Color, roughness: number, subsurface: number }
      fur: { color: Color, density: number, length: number }
      scales: { color: Color, pattern: 'regular' | 'irregular', shine: number }
      feathers: { color: Color, fluffiness: number, iridescence: number }
      bark: { color: Color, roughness: number, pattern: 'smooth' | 'rough' | 'cracked' }
      leaves: { color: Color, transparency: number, seasonalChange: boolean }
    }
    
    // 无机材质
    inorganic: {
      metal: { type: 'iron' | 'gold' | 'silver' | 'copper', oxidation: number }
      stone: { type: 'granite' | 'marble' | 'sandstone' | 'obsidian', weathering: number }
      crystal: { type: 'quartz' | 'diamond' | 'amethyst', clarity: number }
      glass: { transparency: number, tint: Color, frosted: boolean }
      plastic: { color: Color, glossiness: number, flexibility: number }
      ceramic: { color: Color, glaze: boolean, crackPattern: string }
    }
    
    // 魔法材质
    magical: {
      energy: { color: Color, intensity: number, pulsing: boolean }
      ethereal: { transparency: number, glow: number, shimmer: boolean }
      elemental: { element: 'fire' | 'water' | 'earth' | 'air', intensity: number }
      cursed: { darkening: number, corruption: number, aura: boolean }
      blessed: { brightness: number, purity: number, radiance: boolean }
    }
    
    // 特殊材质
    special: {
      liquid: { viscosity: number, flow: boolean, bubbles: boolean }
      gas: { density: number, swirl: boolean, dissipation: number }
      plasma: { temperature: number, electrical: boolean, containment: number }
      void: { absorption: number, distortion: boolean, darkness: number }
    }
  }
  
  // === 表面效果 ===
  surfaceEffects: {
    // 基础属性
    baseProperties: {
      albedo: Color                        // 基础颜色
      roughness: number                    // 粗糙度 0-1
      metallic: number                     // 金属度 0-1
      specular: number                     // 镜面反射 0-1
      emission: number                     // 自发光 0-1
      transparency: number                 // 透明度 0-1
      refraction: number                   // 折射率 1.0-2.0
    }
    
    // 高级效果
    advancedEffects: {
      normalMap: { enabled: boolean, strength: number, pattern: string }
      displacementMap: { enabled: boolean, height: number, pattern: string }
      ambientOcclusion: { enabled: boolean, strength: number, radius: number }
      subsurfaceScattering: { enabled: boolean, depth: number, color: Color }
      
      // 动态效果
      animated: {
        colorShift: { enabled: boolean, speed: number, range: [Color, Color] }
        pulsing: { enabled: boolean, frequency: number, intensity: number }
        flowing: { enabled: boolean, direction: [number, number], speed: number }
        sparkling: { enabled: boolean, density: number, brightness: number }
      }
    }
  }
  
  // === 材质预设库 ===
  materialPresets: {
    // 生物预设
    creatures: {
      human_skin: MaterialPreset
      dragon_scales: MaterialPreset
      wolf_fur: MaterialPreset
      bird_feathers: MaterialPreset
      fish_scales: MaterialPreset
      insect_chitin: MaterialPreset
    }
    
    // 环境预设
    environment: {
      grass: MaterialPreset
      dirt: MaterialPreset
      rock: MaterialPreset
      water: MaterialPreset
      lava: MaterialPreset
      ice: MaterialPreset
    }
    
    // 人造预设
    artificial: {
      steel: MaterialPreset
      wood: MaterialPreset
      cloth: MaterialPreset
      leather: MaterialPreset
      glass: MaterialPreset
      plastic: MaterialPreset
    }
  }
}
```

### 2.3 模型组织和标记系统
```typescript
interface ModelOrganization {
  // === 身体部位标记 ===
  bodyPartMarking: {
    // 自动检测
    autoDetection: {
      enabled: boolean
      algorithm: 'connectivity' | 'shape_analysis' | 'machine_learning'
      confidence: number                   // 检测置信度
      suggestions: BodyPartSuggestion[]
    }
    
    // 手动标记
    manualMarking: {
      // 部位类型
      partTypes: {
        head: { color: '#FF0000', priority: 10 }
        neck: { color: '#FF8800', priority: 9 }
        torso: { color: '#00FF00', priority: 8 }
        arms: { color: '#0088FF', priority: 7 }
        hands: { color: '#0044FF', priority: 6 }
        legs: { color: '#8800FF', priority: 5 }
        feet: { color: '#FF0088', priority: 4 }
        wings: { color: '#FFFF00', priority: 3 }
        tail: { color: '#FF00FF', priority: 2 }
        decoration: { color: '#00FFFF', priority: 1 }
      }
      
      // 标记工具
      markingTools: {
        brush: { size: number, feather: number }
        flood_fill: { tolerance: number, connectivity: '4way' | '8way' | '26way' }
        selection: { mode: 'add' | 'subtract' | 'intersect' }
        smart_select: { similarity: number, region_growing: boolean }
      }
    }
    
    // 部位属性
    partProperties: {
      [partName: string]: {
        isSymmetric: boolean               // 是否对称
        pairedWith: string                 // 配对部位
        parentPart: string                 // 父级部位
        childParts: string[]               // 子级部位
        flexibility: number                // 灵活性 0-1
        importance: number                 // 重要性 0-1
        canDetach: boolean                 // 是否可分离
        
        // 动画相关
        animationPivot: [number, number, number]  // 动画轴心点
        rotationLimits: {
          x: [number, number]              // X轴旋转限制
          y: [number, number]              // Y轴旋转限制
          z: [number, number]              // Z轴旋转限制
        }
      }
    }
  }
  
  // === 模型元数据 ===
  modelMetadata: {
    // 基础信息
    basicInfo: {
      name: string
      id: string
      version: string
      author: string
      description: string
      tags: string[]
      category: string
      subcategory: string
    }
    
    // 技术信息
    technicalInfo: {
      voxelCount: number
      boundingBox: { min: [number, number, number], max: [number, number, number] }
      centerOfMass: [number, number, number]
      complexity: 'simple' | 'moderate' | 'complex' | 'very_complex'
      
      // 性能信息
      renderComplexity: number            // 渲染复杂度
      memoryUsage: number                 // 内存使用量
      recommendedLOD: number              // 推荐LOD等级
    }
    
    // 兼容性信息
    compatibility: {
      supportedAnimationTypes: string[]
      recommendedAnimations: string[]
      bodyStructure: string
      locomotionTypes: string[]
      
      // 尺寸兼容性
      scaleRange: { min: number, max: number }
      aspectRatioLimits: { width: number, height: number, depth: number }
    }
  }
}
```

## 3. 用户界面设计

### 3.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 编辑 视图 工具 模型 帮助                        │
├─────────────────────────────────────────────────────────────┤
│ 工具栏: [新建] [打开] [保存] [撤销] [重做] [预览] [导出]      │
├──────────┬──────────────────────────┬─────────────────────────┤
│          │                          │                         │
│ 工具面板 │     3D编辑视窗            │    属性面板              │
│          │                          │                         │
│ ┌体素工具┐│                          │ ┌─模型信息─┐            │
│ ├材质库─┤│                          │ ├─部位标记─┤            │
│ ├形状库─┤│                          │ ├─材质设置─┤            │
│ ├预设库─┤│                          │ ├─导出设置─┤            │
│ └历史记录┘│                          │ └─兼容性检查┘            │
│          │                          │                         │
├──────────┴──────────────────────────┴─────────────────────────┤
│ 状态栏: 体素数量 | 选中部位 | 内存使用 | 渲染性能              │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 工作流程向导
```typescript
interface WorkflowWizard {
  // === 创建向导 ===
  creationWizard: {
    step1_template: {
      title: '选择模板'
      options: {
        fromScratch: '从空白开始'
        fromPrimitive: '从基础形状开始'
        fromTemplate: '从模板开始'
        fromReference: '从参考图片开始'
      }
    }

    step2_basic_shape: {
      title: '基础形状'
      bodyType: 'humanoid' | 'quadruped' | 'serpentine' | 'flying' | 'aquatic' | 'custom'
      proportions: {
        height: number
        width: number
        depth: number
      }
    }

    step3_detail_level: {
      title: '细节等级'
      complexity: 'low' | 'medium' | 'high' | 'ultra'
      targetUse: 'game_unit' | 'cinematic' | 'prototype' | 'reference'
    }

    step4_materials: {
      title: '材质设置'
      primaryMaterial: MaterialType
      secondaryMaterial: MaterialType
      colorScheme: ColorPalette
    }
  }

  // === 优化向导 ===
  optimizationWizard: {
    step1_analysis: {
      title: '模型分析'
      issues: ModelIssue[]
      suggestions: OptimizationSuggestion[]
    }

    step2_optimization: {
      title: '优化选项'
      reduceVoxels: boolean
      optimizeMaterials: boolean
      simplifyGeometry: boolean
      generateLOD: boolean
    }

    step3_export: {
      title: '导出设置'
      format: 'voxel' | 'mesh' | 'both'
      quality: 'draft' | 'standard' | 'high' | 'ultra'
      compression: boolean
    }
  }
}
```
