# 特效编辑器规格文档

## 1. 概述

特效编辑器专门用于创建各种视觉特效，包括粒子效果、光效、扭曲效果等。为技能编辑器、动画编辑器和单位编辑器提供特效资源，支持从简单的火花效果到复杂的魔法阵特效。

## 2. 核心功能

### 2.1 粒子系统
```typescript
interface ParticleSystem {
  // === 粒子发射器 ===
  emitters: {
    // 发射器类型
    emitterTypes: {
      point: {
        position: [number, number, number]
        direction: [number, number, number]
        spread: number                       // 发射角度
      }
      
      area: {
        shape: 'box' | 'sphere' | 'cylinder' | 'cone' | 'plane'
        size: [number, number, number]
        distribution: 'uniform' | 'edge' | 'surface' | 'volume'
      }
      
      mesh: {
        meshSource: string                   // 网格来源
        emitFrom: 'vertices' | 'edges' | 'faces'
        density: number
      }
      
      trail: {
        followTarget: string                 // 跟随目标
        trailLength: number
        segmentCount: number
      }
    }
    
    // 发射参数
    emissionParameters: {
      rate: { value: number, variation: number }        // 发射速率
      burst: { count: number, interval: number }        // 爆发发射
      lifetime: { min: number, max: number }            // 粒子生命周期
      
      // 初始属性
      initialVelocity: {
        speed: { min: number, max: number }
        direction: { x: number, y: number, z: number }
        randomness: number
      }
      
      initialSize: { min: number, max: number }
      initialColor: { r: number, g: number, b: number, a: number }
      initialRotation: { min: number, max: number }
    }
  }
  
  // === 粒子行为 ===
  particleBehavior: {
    // 物理行为
    physics: {
      gravity: { x: number, y: number, z: number }
      drag: number                           // 阻力
      bounce: { enabled: boolean, damping: number }
      collision: { enabled: boolean, layers: string[] }
    }
    
    // 运动行为
    motion: {
      // 力场
      forces: {
        wind: { direction: [number, number, number], strength: number }
        vortex: { center: [number, number, number], strength: number, axis: [number, number, number] }
        attractor: { position: [number, number, number], strength: number, falloff: number }
        turbulence: { strength: number, frequency: number, octaves: number }
      }
      
      // 路径跟随
      pathFollowing: {
        enabled: boolean
        path: PathDefinition
        speed: number
        lookAhead: number
      }
      
      // 群体行为
      flocking: {
        enabled: boolean
        separation: number
        alignment: number
        cohesion: number
        neighborRadius: number
      }
    }
    
    // 生命周期行为
    lifecycle: {
      // 尺寸变化
      sizeOverTime: {
        curve: AnimationCurve
        randomness: number
      }
      
      // 颜色变化
      colorOverTime: {
        gradient: ColorGradient
        randomness: number
      }
      
      // 透明度变化
      alphaOverTime: {
        curve: AnimationCurve
        fadeIn: number
        fadeOut: number
      }
      
      // 旋转变化
      rotationOverTime: {
        angularVelocity: number
        acceleration: number
        randomness: number
      }
    }
  }
  
  // === 渲染设置 ===
  rendering: {
    // 材质设置
    material: {
      type: 'sprite' | 'mesh' | 'trail' | 'beam'
      texture: string
      shader: string
      
      // 混合模式
      blendMode: 'alpha' | 'additive' | 'multiply' | 'screen' | 'overlay'
      
      // 渲染属性
      properties: {
        tiling: [number, number]
        offset: [number, number]
        emission: number
        metallic: number
        roughness: number
      }
    }
    
    // 排序和层级
    sorting: {
      sortMode: 'none' | 'distance' | 'age' | 'speed'
      renderQueue: number
      layer: string
    }
    
    // 性能设置
    performance: {
      maxParticles: number
      cullingDistance: number
      lodLevels: LODLevel[]
      batchingEnabled: boolean
    }
  }
}
```

### 2.2 光效系统
```typescript
interface LightEffectSystem {
  // === 光源类型 ===
  lightTypes: {
    // 点光源
    pointLight: {
      position: [number, number, number]
      color: { r: number, g: number, b: number }
      intensity: number
      range: number
      falloff: 'linear' | 'quadratic' | 'custom'
      
      // 动画属性
      animation: {
        flicker: { enabled: boolean, frequency: number, intensity: number }
        pulse: { enabled: boolean, frequency: number, amplitude: number }
        colorShift: { enabled: boolean, colors: Color[], speed: number }
      }
    }
    
    // 聚光灯
    spotLight: {
      position: [number, number, number]
      direction: [number, number, number]
      color: { r: number, g: number, b: number }
      intensity: number
      range: number
      innerAngle: number
      outerAngle: number
      
      // 特殊效果
      effects: {
        volumetric: { enabled: boolean, density: number, scattering: number }
        gobo: { enabled: boolean, texture: string, rotation: number }
        cookies: { enabled: boolean, texture: string, scale: number }
      }
    }
    
    // 区域光
    areaLight: {
      shape: 'rectangle' | 'circle' | 'tube'
      size: [number, number]
      position: [number, number, number]
      rotation: [number, number, number]
      color: { r: number, g: number, b: number }
      intensity: number
      
      // 软阴影
      softShadows: {
        enabled: boolean
        penumbra: number
        samples: number
      }
    }
    
    // 环境光
    environmentLight: {
      type: 'skybox' | 'gradient' | 'hdri'
      skybox: string
      gradient: {
        topColor: Color
        middleColor: Color
        bottomColor: Color
      }
      hdri: string
      intensity: number
      rotation: number
    }
  }
  
  // === 光效特效 ===
  lightEffects: {
    // 光束效果
    beams: {
      laser: {
        startPoint: [number, number, number]
        endPoint: [number, number, number]
        width: number
        color: Color
        intensity: number
        
        // 动画
        animation: {
          sweep: { enabled: boolean, speed: number, arc: number }
          pulse: { enabled: boolean, frequency: number }
          noise: { enabled: boolean, strength: number, frequency: number }
        }
      }
      
      searchlight: {
        source: [number, number, number]
        direction: [number, number, number]
        length: number
        width: number
        color: Color
        volumetric: boolean
      }
    }
    
    // 光环效果
    halos: {
      simple: {
        position: [number, number, number]
        radius: number
        color: Color
        intensity: number
        falloff: number
      }
      
      complex: {
        position: [number, number, number]
        innerRadius: number
        outerRadius: number
        colorGradient: ColorGradient
        rotation: number
        segments: number
      }
    }
    
    // 闪电效果
    lightning: {
      chain: {
        startPoint: [number, number, number]
        endPoint: [number, number, number]
        segments: number
        randomness: number
        thickness: number
        color: Color
        
        // 分支
        branches: {
          enabled: boolean
          probability: number
          length: number
          angle: number
        }
      }
      
      area: {
        center: [number, number, number]
        radius: number
        boltCount: number
        frequency: number
        color: Color
      }
    }
  }

  // === 战斗特效 ===
  combat: {
    // 武器特效
    weapons: {
      swordSlash: {
        name: '剑气斩击'
        components: {
          slash: { type: 'trail', preset: 'sword_trail' }
          sparks: { type: 'particle', preset: 'metal_sparks' }
          shockwave: { type: 'distortion', preset: 'air_slash' }
          glow: { type: 'light', preset: 'weapon_glow' }
        }
        parameters: {
          trailLength: { default: 2.0, range: [1.0, 5.0] }
          sparkIntensity: { default: 1.0, range: [0.3, 2.0] }
          glowColor: { default: 'white', options: ['white', 'blue', 'red', 'gold'] }
        }
      }

      arrowTrail: {
        name: '箭矢轨迹'
        components: {
          trail: { type: 'trail', preset: 'arrow_trail' }
          whistle: { type: 'particle', preset: 'air_whistle' }
          impact: { type: 'particle', preset: 'arrow_impact' }
        }
        parameters: {
          speed: { default: 1.0, range: [0.5, 3.0] }
          trailWidth: { default: 0.1, range: [0.05, 0.3] }
          impactForce: { default: 1.0, range: [0.5, 2.0] }
        }
      }
    }

    // 爆炸特效
    explosions: {
      basicExplosion: {
        name: '基础爆炸'
        components: {
          flash: { type: 'light', preset: 'explosion_flash' }
          fireball: { type: 'particle', preset: 'explosion_fire' }
          smoke: { type: 'particle', preset: 'explosion_smoke' }
          debris: { type: 'particle', preset: 'explosion_debris' }
          shockwave: { type: 'distortion', preset: 'explosion_wave' }
        }
        parameters: {
          scale: { default: 1.0, range: [0.3, 5.0] }
          intensity: { default: 1.0, range: [0.5, 2.0] }
          smokeAmount: { default: 1.0, range: [0.3, 2.0] }
          debrisCount: { default: 50, range: [20, 200] }
        }
      }
    }
  }

  // === 环境特效 ===
  environmental: {
    // 天气特效
    weather: {
      rain: {
        name: '降雨'
        components: {
          raindrops: { type: 'particle', preset: 'rain_drops' }
          splashes: { type: 'particle', preset: 'rain_splashes' }
          mist: { type: 'volumetric', preset: 'rain_mist' }
          puddles: { type: 'surface', preset: 'water_puddles' }
        }
        parameters: {
          intensity: { default: 1.0, range: [0.1, 3.0] }
          windDirection: { default: [0, -1, 0], range: 'vector' }
          dropSize: { default: 1.0, range: [0.5, 2.0] }
          mistAmount: { default: 0.3, range: [0.0, 1.0] }
        }
      }

      snow: {
        name: '降雪'
        components: {
          snowflakes: { type: 'particle', preset: 'snow_flakes' }
          accumulation: { type: 'surface', preset: 'snow_layer' }
          wind: { type: 'particle', preset: 'snow_wind' }
          fog: { type: 'volumetric', preset: 'snow_fog' }
        }
        parameters: {
          intensity: { default: 1.0, range: [0.1, 2.0] }
          flakeSize: { default: 1.0, range: [0.5, 3.0] }
          windStrength: { default: 0.5, range: [0.0, 2.0] }
          accumulation: { default: 0.5, range: [0.0, 1.0] }
        }
      }
    }

    // 自然特效
    nature: {
      waterfall: {
        name: '瀑布'
        components: {
          water: { type: 'particle', preset: 'falling_water' }
          mist: { type: 'particle', preset: 'water_mist' }
          splash: { type: 'particle', preset: 'water_splash' }
          sound: { type: 'audio', preset: 'waterfall_sound' }
        }
        parameters: {
          flow: { default: 1.0, range: [0.3, 3.0] }
          height: { default: 5.0, range: [1.0, 20.0] }
          width: { default: 2.0, range: [0.5, 10.0] }
          mistAmount: { default: 0.7, range: [0.2, 1.0] }
        }
      }

      campfire: {
        name: '篝火'
        components: {
          flames: { type: 'particle', preset: 'campfire_flames' }
          embers: { type: 'particle', preset: 'campfire_embers' }
          smoke: { type: 'particle', preset: 'campfire_smoke' }
          light: { type: 'point_light', preset: 'fire_light' }
          heat: { type: 'distortion', preset: 'heat_shimmer' }
        }
        parameters: {
          size: { default: 1.0, range: [0.5, 2.0] }
          intensity: { default: 1.0, range: [0.3, 2.0] }
          smokeAmount: { default: 0.8, range: [0.2, 1.5] }
          emberCount: { default: 20, range: [5, 50] }
        }
      }
    }
  }
}
```

## 4. 用户界面设计

### 4.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 编辑 特效 工具 预览 帮助                        │
├─────────────────────────────────────────────────────────────┤
│ 工具栏: [新建] [打开] [保存] [播放] [暂停] [停止] [导出]      │
├──────────┬──────────────────────────┬─────────────────────────┤
│          │                          │                         │
│ 特效库   │     3D预览窗口            │    组件面板              │
│ 面板     │                          │                         │
│          │                          │ ┌─粒子系统─┐            │
│ ┌模板库─┐│                          │ ├─光效系统─┤            │
│ ├组件库─┤│                          │ ├─后处理──┤            │
│ ├预设库─┤│                          │ ├─材质设置─┤            │
│ ├场景库─┤│                          │ └─渲染设置─┘            │
│ └历史记录┘│                          │                         │
│          │                          │    时间轴面板            │
│          │                          │ ┌─关键帧──┐            │
│          │                          │ ├─曲线编辑─┤            │
│          │                          │ ├─事件标记─┤            │
│          │                          │ └─播放控制─┘            │
├──────────┴──────────────────────────┴─────────────────────────┤
│ 状态栏: 粒子数量 | 渲染时间 | 内存使用 | 播放状态              │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 特效组合系统
```typescript
interface EffectComposition {
  // === 层级系统 ===
  layerSystem: {
    // 层级类型
    layerTypes: {
      background: { priority: 0, blendMode: 'normal' }
      environment: { priority: 10, blendMode: 'normal' }
      objects: { priority: 20, blendMode: 'normal' }
      effects: { priority: 30, blendMode: 'additive' }
      ui: { priority: 40, blendMode: 'alpha' }
    }

    // 层级操作
    layerOperations: {
      add: { name: string, type: LayerType }
      remove: { layer: Layer }
      reorder: { layer: Layer, newIndex: number }
      duplicate: { layer: Layer }

      // 层级属性
      layerProperties: {
        visible: boolean
        locked: boolean
        opacity: number
        blendMode: BlendMode
        maskLayer: Layer | null
      }
    }
  }

  // === 组件组合 ===
  componentComposition: {
    // 组件关系
    relationships: {
      parent: Component | null
      children: Component[]
      dependencies: Component[]

      // 同步设置
      synchronization: {
        timing: boolean                  // 时间同步
        transform: boolean               // 变换同步
        parameters: string[]             // 参数同步
      }
    }

    // 组合预设
    compositionPresets: {
      explosion: {
        components: ['flash', 'fireball', 'smoke', 'debris', 'shockwave']
        timing: { flash: 0, fireball: 0.1, smoke: 0.2, debris: 0.1, shockwave: 0.05 }
        relationships: { parent: 'explosion_center', children: 'all' }
      }

      magic_circle: {
        components: ['base_circle', 'runes', 'energy_flow', 'glow', 'particles']
        timing: { base_circle: 0, runes: 0.5, energy_flow: 1.0, glow: 0, particles: 1.5 }
        relationships: { parent: 'base_circle', children: ['runes', 'glow'] }
      }
    }
  }
}
```

---

## 总结

特效编辑器作为视觉特效制作工具，提供了：

### 核心功能
- **完整的粒子系统**: 多种发射器、丰富的行为控制、专业的渲染设置
- **专业的光效系统**: 各种光源类型、特殊光效、体积光效果
- **强大的后处理**: 屏幕空间效果、体积效果、颜色处理
- **丰富的模板库**: 魔法、战斗、环境等各类特效模板

### 用户体验
- **模块化组合**: 通过组件组合创建复杂特效
- **实时预览**: 即时查看特效效果
- **层级管理**: 专业的层级和混合系统
- **参数化控制**: 直观的参数调节界面

### 系统集成
- **标准化输出**: 为技能编辑器和动画编辑器提供特效资源
- **性能优化**: 内置LOD和性能管理
- **兼容性保证**: 与其他模块的无缝集成
- **资源管理**: 完整的特效库管理系统
```

### 2.3 后处理效果
```typescript
interface PostProcessingEffects {
  // === 屏幕空间效果 ===
  screenSpaceEffects: {
    // 扭曲效果
    distortion: {
      heatHaze: {
        strength: number
        frequency: number
        speed: number
        texture: string
      }
      
      shockwave: {
        center: [number, number]
        radius: number
        strength: number
        thickness: number
        speed: number
      }
      
      ripple: {
        center: [number, number]
        amplitude: number
        frequency: number
        speed: number
        damping: number
      }
    }
    
    // 模糊效果
    blur: {
      gaussian: {
        radius: number
        samples: number
        direction: 'horizontal' | 'vertical' | 'both'
      }
      
      motionBlur: {
        strength: number
        samples: number
        velocityScale: number
      }
      
      radialBlur: {
        center: [number, number]
        strength: number
        samples: number
      }
    }
    
    // 颜色效果
    colorEffects: {
      colorGrading: {
        contrast: number
        brightness: number
        saturation: number
        hue: number
        gamma: number
      }
      
      toneMapping: {
        type: 'linear' | 'reinhard' | 'aces' | 'uncharted2'
        exposure: number
        whitePoint: number
      }
      
      colorFilter: {
        color: Color
        blendMode: 'multiply' | 'screen' | 'overlay' | 'add'
        strength: number
      }
    }
  }
  
  // === 体积效果 ===
  volumetricEffects: {
    // 雾效
    fog: {
      type: 'linear' | 'exponential' | 'exponential_squared'
      color: Color
      density: number
      start: number
      end: number
      
      // 动画雾
      animated: {
        enabled: boolean
        speed: [number, number, number]
        turbulence: number
        noise: string
      }
    }
    
    // 体积光
    volumetricLighting: {
      enabled: boolean
      scattering: number
      extinction: number
      samples: number
      jittering: number
      
      // 光轴
      lightShafts: {
        enabled: boolean
        intensity: number
        decay: number
        exposure: number
      }
    }
    
    // 云效果
    clouds: {
      type: 'procedural' | 'texture' | 'volumetric'
      density: number
      coverage: number
      height: [number, number]
      
      // 程序化云
      procedural: {
        octaves: number
        frequency: number
        amplitude: number
        speed: [number, number, number]
      }
    }
  }
}
```

## 3. 特效模板库

### 3.1 魔法特效模板
```typescript
interface MagicEffectTemplates {
  // === 元素魔法 ===
  elemental: {
    // 火系魔法
    fire: {
      fireball: {
        name: '火球术'
        components: {
          core: { type: 'particle', preset: 'fire_core' }
          flames: { type: 'particle', preset: 'fire_flames' }
          sparks: { type: 'particle', preset: 'fire_sparks' }
          glow: { type: 'light', preset: 'fire_glow' }
          trail: { type: 'trail', preset: 'fire_trail' }
        }
        parameters: {
          size: { default: 1.0, range: [0.5, 3.0] }
          intensity: { default: 1.0, range: [0.3, 2.0] }
          speed: { default: 1.0, range: [0.5, 2.0] }
          color: { default: 'orange', options: ['red', 'orange', 'blue', 'green'] }
        }
      }
      
      fireWall: {
        name: '火墙术'
        components: {
          base: { type: 'particle', preset: 'fire_wall_base' }
          flames: { type: 'particle', preset: 'fire_wall_flames' }
          heat: { type: 'distortion', preset: 'heat_haze' }
          light: { type: 'area_light', preset: 'fire_illumination' }
        }
        parameters: {
          width: { default: 5.0, range: [2.0, 20.0] }
          height: { default: 3.0, range: [1.0, 10.0] }
          duration: { default: 10.0, range: [3.0, 60.0] }
          intensity: { default: 1.0, range: [0.5, 2.0] }
        }
      }
    }
    
    // 冰系魔法
    ice: {
      iceSpike: {
        name: '冰刺术'
        components: {
          spike: { type: 'mesh', preset: 'ice_spike_mesh' }
          crystals: { type: 'particle', preset: 'ice_crystals' }
          frost: { type: 'particle', preset: 'frost_effect' }
          refraction: { type: 'distortion', preset: 'ice_refraction' }
        }
        parameters: {
          size: { default: 1.0, range: [0.5, 2.0] }
          sharpness: { default: 1.0, range: [0.5, 2.0] }
          transparency: { default: 0.7, range: [0.3, 0.9] }
          frostAmount: { default: 0.5, range: [0.0, 1.0] }
        }
      }
      
      blizzard: {
        name: '暴风雪'
        components: {
          snow: { type: 'particle', preset: 'heavy_snow' }
          wind: { type: 'particle', preset: 'wind_lines' }
          fog: { type: 'volumetric', preset: 'ice_fog' }
          temperature: { type: 'distortion', preset: 'cold_air' }
        }
        parameters: {
          intensity: { default: 1.0, range: [0.3, 2.0] }
          windSpeed: { default: 1.0, range: [0.5, 3.0] }
          visibility: { default: 0.5, range: [0.1, 1.0] }
          area: { default: 10.0, range: [5.0, 50.0] }
        }
      }
    }
    
    // 雷系魔法
    lightning: {
      lightningBolt: {
        name: '闪电术'
        components: {
          bolt: { type: 'lightning', preset: 'main_bolt' }
          branches: { type: 'lightning', preset: 'side_branches' }
          flash: { type: 'light', preset: 'lightning_flash' }
          sparks: { type: 'particle', preset: 'electric_sparks' }
          afterglow: { type: 'light', preset: 'electric_afterglow' }
        }
        parameters: {
          power: { default: 1.0, range: [0.5, 3.0] }
          branches: { default: 3, range: [1, 8] }
          duration: { default: 0.2, range: [0.1, 0.5] }
          color: { default: 'white', options: ['white', 'blue', 'purple', 'red'] }
        }
      }
    }
  }
}
