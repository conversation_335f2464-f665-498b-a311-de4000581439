# 地形生成器详细规格文档

## 1. 概述

地形生成器采用**方案A: 完全分离**的设计理念，类似魔兽争霸3地图编辑器(WE)：
- **基础地形**: 使用大方块+中方块生成主要地貌
- **装饰系统**: 使用小方块+微方块作为独立装饰层
- **用户控制**: 提供灵活的参数选择和随机分布控制

## 2. 多尺度方块分层系统

### 2.1 分层定义
```typescript
interface TerrainLayerSystem {
  // === 基础地形层 (大方块 100cm) ===
  baseLayer: {
    purpose: '主要地形轮廓和大型地貌'
    content: ['bedrock', 'major_landforms', 'mountain_ranges', 'valleys']
    renderDistance: 'unlimited'
    priority: 'highest'
  }
  
  // === 细节地形层 (中方块 50cm) ===
  detailLayer: {
    purpose: '斜坡处理、小型地貌和地形过渡'
    content: ['slopes', 'small_hills', 'rock_outcrops', 'terrain_transitions']
    renderDistance: '500m'
    priority: 'high'
  }
  
  // === 装饰层 (小方块 20cm) - 独立系统 ===
  decorationLayer: {
    purpose: '植被、岩石、表面装饰'
    content: ['trees', 'bushes', 'rocks', 'grass_patches']
    renderDistance: '200m'
    priority: 'medium'
    independent: true  // 独立于地形生成
  }
  
  // === 微装饰层 (微方块 5cm) - 独立系统 ===
  microLayer: {
    purpose: '精细装饰和纹理细节'
    content: ['flowers', 'moss', 'pebbles', 'debris']
    renderDistance: '50m'
    priority: 'low'
    independent: true  // 独立于地形生成
  }
}
```

## 3. 基础地形生成参数

### 3.1 地形类型和规模
```typescript
interface BaseTerrainParams {
  // === 主要地形类型 ===
  primaryTerrain: 'mountains' | 'hills' | 'plains' | 'valleys' | 'plateaus' |
                  'canyons' | 'coastal' | 'desert' | 'badlands' | 'karst' |
                  'snowlands' | 'urban_roads' | 'rural_roads' | 'muddy_terrain' |
                  'rocky_terrain' | 'volcanic_terrain' | 'frozen_terrain' |
                  'swampy_terrain' | 'sandy_beaches' | 'cliff_terrain'
  
  // === 地形规模控制 ===
  scale: {
    totalArea: { width: number, depth: number }  // 地图尺寸
    heightRange: { min: number, max: number }    // 高度范围
    verticalExaggeration: number                 // 垂直夸张 0.5-3.0
    terrainComplexity: number                    // 地形复杂度 0-1
  }
  
  // === 地形特征密度 ===
  featureDensity: {
    peakDensity: number          // 山峰密度 0-1
    valleyDensity: number        // 山谷密度 0-1
    ridgeDensity: number         // 山脊密度 0-1
    saddleDensity: number        // 鞍部密度 0-1
  }
  
  // === 地形对称性 ===
  symmetry: {
    type: 'none' | 'bilateral' | 'radial' | 'quadrant'
    axis: number                 // 对称轴角度 0-360°
    symmetryStrength: number     // 对称强度 0-1
  }
}
```

### 3.2 斜坡和过渡系统
```typescript
interface SlopeTransitionParams {
  // === 斜坡生成策略 ===
  slopeGeneration: {
    // 斜坡平滑度
    smoothness: number           // 0-1, 0=阶梯状, 1=完全平滑
    
    // 斜坡类型分布
    slopeDistribution: {
      gentleSlopes: number       // 缓坡比例 (0-15°)
      moderateSlopes: number     // 中坡比例 (15-30°)
      steepSlopes: number        // 陡坡比例 (30-60°)
      cliffs: number             // 悬崖比例 (60-90°)
    }
    
    // 阶梯化控制
    terracing: {
      enabled: boolean
      naturalTerracing: boolean  // 自然阶地
      artificialTerracing: boolean // 人工梯田
      stepHeight: { min: number, max: number }
      stepWidth: { min: number, max: number }
    }
    
    // 过渡区域
    transitionZones: {
      width: number              // 过渡带宽度
      blendingMethod: 'linear' | 'smooth' | 'natural' | 'stepped'
      feathering: number         // 边缘羽化程度
    }
  }
  
  // === 侵蚀模拟 ===
  erosionSimulation: {
    waterErosion: {
      enabled: boolean
      intensity: number          // 侵蚀强度 0-1
      iterations: number         // 模拟迭代次数
      
      // 侵蚀类型
      thermalErosion: boolean    // 热力侵蚀
      hydraulicErosion: boolean  // 水力侵蚀
      sedimentTransport: boolean // 沉积物搬运
    }
    
    windErosion: {
      enabled: boolean
      prevailingWind: number     // 主导风向 0-360°
      intensity: number          // 风蚀强度
      
      // 风蚀效果
      deflation: boolean         // 吹蚀
      sandBlasting: boolean      // 风沙磨蚀
      duneFormation: boolean     // 沙丘形成
    }
  }
}
```

## 4. 水文系统参数

### 4.1 河流系统
```typescript
interface RiverSystemParams {
  // === 河流网络 ===
  riverNetwork: {
    enabled: boolean
    density: number              // 河流密度 0-1
    
    // 河流等级系统
    streamOrder: {
      maxOrder: number           // 最大河流等级 1-7
      branchingRatio: number     // 分支比 2-5
      lengthRatio: number        // 长度比 1.5-3.0
    }
    
    // 河流起源
    sources: {
      springFed: boolean         // 泉水补给
      glacialFed: boolean        // 冰川补给
      rainFed: boolean           // 雨水补给
      groundwaterFed: boolean    // 地下水补给
    }
  }
  
  // === 河流形态 ===
  riverMorphology: {
    // 河道特征
    channelWidth: { min: number, max: number }
    channelDepth: { min: number, max: number }
    bankSlope: { min: number, max: number }
    
    // 河流模式
    pattern: {
      straight: number           // 直流河段比例
      meandering: number         // 蜿蜒河段比例
      braided: number            // 辫状河段比例
      anastomosing: number       // 网状河段比例
    }
    
    // 蜿蜒特征
    meandering: {
      amplitude: { min: number, max: number }    // 蜿蜒振幅
      wavelength: { min: number, max: number }   // 蜿蜒波长
      sinuosity: { min: number, max: number }    // 弯曲度
    }
  }
  
  // === 河流特征 ===
  riverFeatures: {
    // 纵向特征
    longitudinal: {
      waterfalls: {
        enabled: boolean
        frequency: number        // 瀑布频率
        height: { min: number, max: number }
      }
      
      rapids: {
        enabled: boolean
        frequency: number        // 急流频率
        length: { min: number, max: number }
      }
      
      pools: {
        enabled: boolean
        frequency: number        // 深潭频率
        depth: { min: number, max: number }
      }
    }
    
    // 横向特征
    lateral: {
      floodplains: {
        enabled: boolean
        width: { min: number, max: number }
        terraceCount: number     // 阶地数量
      }
      
      oxbowLakes: {
        enabled: boolean
        frequency: number        // 牛轭湖频率
        preservationTime: number // 保存时间
      }
      
      levees: {
        enabled: boolean
        height: { min: number, max: number }
        naturalLevees: boolean   // 天然堤
        artificialLevees: boolean // 人工堤
      }
    }
  }
}
```

### 4.2 湖泊和湿地系统
```typescript
interface LakeWetlandParams {
  // === 湖泊系统 ===
  lakes: {
    enabled: boolean
    
    // 湖泊数量和分布
    distribution: {
      count: { min: number, max: number }
      clustering: number         // 聚集程度 0-1
      elevationPreference: 'high' | 'low' | 'mixed' | 'none'
    }
    
    // 湖泊类型
    lakeTypes: {
      // 成因类型
      glacialLakes: boolean      // 冰川湖
      tectonicLakes: boolean     // 构造湖
      volcanicLakes: boolean     // 火山湖
      oxbowLakes: boolean        // 牛轭湖
      karstLakes: boolean        // 喀斯特湖
      
      // 形态类型
      deepLakes: boolean         // 深水湖
      shallowLakes: boolean      // 浅水湖
      seasonalLakes: boolean     // 季节性湖泊
    }
    
    // 湖泊特征
    characteristics: {
      size: { min: number, max: number }
      depth: { min: number, max: number }
      shape: 'circular' | 'elongated' | 'irregular' | 'dendritic' | 'mixed'
      
      // 湖岸特征
      shoreline: {
        complexity: number       // 岸线复杂度 0-1
        hasBeaches: boolean      // 沙滩
        hasCliffs: boolean       // 湖岸悬崖
        hasMarshes: boolean      // 湖滨沼泽
      }
    }
  }
  
  // === 湿地系统 ===
  wetlands: {
    enabled: boolean
    
    // 湿地类型
    wetlandTypes: {
      marshes: boolean           // 沼泽
      swamps: boolean            // 泥沼
      bogs: boolean              // 泥炭沼
      fens: boolean              // 富营养沼泽
      
      // 特殊湿地
      tidalWetlands: boolean     // 潮汐湿地
      seasonalWetlands: boolean  // 季节性湿地
      springWetlands: boolean    // 泉水湿地
    }
    
    // 湿地分布
    distribution: {
      lowlandPreference: number  // 低地偏好 0-1
      riparianZones: boolean     // 河岸带
      depressionAreas: boolean   // 洼地
      seepageAreas: boolean      // 渗流区
    }
    
    // 湿地特征
    characteristics: {
      waterDepth: { min: number, max: number }
      seasonalVariation: number  // 季节变化幅度
      connectivity: number       // 连通性 0-1
    }
  }
}
```

## 5. 独立装饰系统参数 (类似WE装饰物)

### 5.1 装饰物分类和控制
```typescript
interface DecorationSystemParams {
  // === 装饰物总开关 ===
  decorationEnabled: boolean

  // === 植被装饰 ===
  vegetation: {
    enabled: boolean

    // 树木系统
    trees: {
      enabled: boolean
      density: number            // 密度 0-1
      variety: number            // 种类多样性 0-1

      // 树木类型
      treeTypes: {
        conifers: boolean        // 针叶树
        deciduous: boolean       // 阔叶树
        palms: boolean           // 棕榈树
        fruit: boolean           // 果树
        dead: boolean            // 枯树
      }

      // 分布模式
      distribution: {
        pattern: 'random' | 'clustered' | 'linear' | 'scattered' | 'forest_patches'
        clusterSize: { min: number, max: number }
        clusterDensity: number

        // 环境响应
        elevationResponse: boolean    // 海拔响应
        slopeResponse: boolean        // 坡度响应
        moistureResponse: boolean     // 湿度响应
        aspectResponse: boolean       // 坡向响应
      }

      // 树木特征
      characteristics: {
        height: { min: number, max: number }
        crownSize: { min: number, max: number }
        trunkThickness: { min: number, max: number }
        ageVariation: boolean         // 年龄变化
        seasonalChange: boolean       // 季节变化
      }
    }

    // 灌木系统
    shrubs: {
      enabled: boolean
      density: number

      // 灌木类型
      shrubTypes: {
        berry: boolean           // 浆果灌木
        flowering: boolean       // 开花灌木
        thorny: boolean          // 荆棘灌木
        evergreen: boolean       // 常绿灌木
        seasonal: boolean        // 季节性灌木
      }

      // 分布特征
      distribution: {
        undergrowth: boolean     // 林下灌木
        openArea: boolean        // 开阔地灌木
        edgeEffect: boolean      // 边缘效应
        patchSize: { min: number, max: number }
      }
    }

    // 草本植物
    herbs: {
      enabled: boolean
      density: number

      // 草本类型
      herbTypes: {
        grasses: boolean         // 禾草
        wildflowers: boolean     // 野花
        ferns: boolean           // 蕨类
        mosses: boolean          // 苔藓
        lichens: boolean         // 地衣
      }

      // 分布模式
      distribution: {
        meadows: boolean         // 草甸
        forestFloor: boolean     // 林地
        rockyCrevices: boolean   // 岩石缝隙
        wetAreas: boolean        // 湿润区域
      }
    }
  }

  // === 岩石装饰 ===
  rocks: {
    enabled: boolean

    // 岩石类型
    rockTypes: {
      boulders: {
        enabled: boolean
        density: number
        size: { min: number, max: number }
        distribution: 'random' | 'clustered' | 'linear' | 'avalanche_cone'
      }

      outcrops: {
        enabled: boolean
        density: number
        size: { min: number, max: number }
        orientation: 'random' | 'geological' | 'weathering_pattern'
      }

      scree: {
        enabled: boolean
        density: number
        slopePreference: number  // 坡度偏好
        particleSize: { min: number, max: number }
      }

      erratics: {
        enabled: boolean         // 漂砾
        density: number
        glacialOrigin: boolean
        size: { min: number, max: number }
      }
    }

    // 岩石特征
    characteristics: {
      weathering: number         // 风化程度 0-1
      mossGrowth: boolean        // 苔藓生长
      lichen: boolean            // 地衣覆盖
      fracturing: number         // 裂隙程度 0-1
    }
  }

  // === 水体装饰 ===
  waterFeatures: {
    enabled: boolean

    // 小型水体
    smallWaterBodies: {
      ponds: {
        enabled: boolean
        count: { min: number, max: number }
        size: { min: number, max: number }
        depth: { min: number, max: number }
      }

      springs: {
        enabled: boolean
        count: { min: number, max: number }
        flowRate: { min: number, max: number }
        temperature: 'cold' | 'warm' | 'hot' | 'mixed'
      }

      seeps: {
        enabled: boolean         // 渗流
        density: number
        seasonalVariation: boolean
      }
    }

    // 水体特征
    waterCharacteristics: {
      clarity: 'clear' | 'murky' | 'stained' | 'mixed'
      vegetation: boolean        // 水生植物
      wildlife: boolean          // 水生动物迹象
      iceFormation: boolean      // 结冰现象
    }
  }
}
```

### 5.2 装饰物智能分布算法
```typescript
interface IntelligentDistribution {
  // === 环境响应系统 ===
  environmentalFactors: {
    // 地形响应
    topographic: {
      elevation: {
        enabled: boolean
        zones: {
          lowland: { range: [number, number], preference: number }
          midland: { range: [number, number], preference: number }
          highland: { range: [number, number], preference: number }
          alpine: { range: [number, number], preference: number }
        }
      }

      slope: {
        enabled: boolean
        preferences: {
          flat: number           // 平地偏好 0-1
          gentle: number         // 缓坡偏好
          moderate: number       // 中坡偏好
          steep: number          // 陡坡偏好
        }
      }

      aspect: {
        enabled: boolean
        preferences: {
          north: number          // 北坡偏好
          south: number          // 南坡偏好
          east: number           // 东坡偏好
          west: number           // 西坡偏好
        }
      }
    }

    // 水文响应
    hydrological: {
      waterProximity: {
        enabled: boolean
        optimalDistance: { min: number, max: number }
        decayFunction: 'linear' | 'exponential' | 'gaussian'
      }

      drainageDensity: {
        enabled: boolean
        preference: number       // 排水密度偏好
      }

      soilMoisture: {
        enabled: boolean
        preferences: {
          dry: number            // 干燥土壤偏好
          moist: number          // 湿润土壤偏好
          wet: number            // 潮湿土壤偏好
          saturated: number      // 饱和土壤偏好
        }
      }
    }

    // 气候响应
    climatic: {
      temperature: {
        enabled: boolean
        optimalRange: { min: number, max: number }
        tolerance: number        // 温度容忍度
      }

      precipitation: {
        enabled: boolean
        optimalRange: { min: number, max: number }
        seasonality: boolean     // 季节性降水响应
      }

      windExposure: {
        enabled: boolean
        tolerance: number        // 风暴露容忍度
        shelteredPreference: number // 避风偏好
      }
    }
  }

  // === 生态关系系统 ===
  ecologicalRelations: {
    // 种间关系
    interspecific: {
      competition: {
        enabled: boolean
        competitionRadius: number
        competitionStrength: number
      }

      facilitation: {
        enabled: boolean         // 促进作用
        facilitationRadius: number
        facilitationStrength: number
      }

      succession: {
        enabled: boolean         // 演替关系
        pioneerSpecies: string[] // 先锋物种
        climaxSpecies: string[]  // 顶极物种
      }
    }

    // 种群动态
    population: {
      dispersal: {
        enabled: boolean
        dispersalDistance: { min: number, max: number }
        dispersalMethod: 'wind' | 'water' | 'animal' | 'gravity' | 'mixed'
      }

      recruitment: {
        enabled: boolean
        recruitmentRate: number  // 补充率
        mortalityRate: number    // 死亡率
      }
    }
  }

  // === 人为影响系统 ===
  anthropogenic: {
    disturbance: {
      enabled: boolean

      // 干扰类型
      disturbanceTypes: {
        fire: {
          enabled: boolean
          frequency: number      // 火灾频率
          intensity: number      // 火灾强度
          recoveryTime: number   // 恢复时间
        }

        logging: {
          enabled: boolean
          selectivity: number    // 选择性采伐
          intensity: number      // 采伐强度
        }

        grazing: {
          enabled: boolean
          intensity: number      // 放牧强度
          seasonality: boolean   // 季节性放牧
        }
      }
    }

    management: {
      enabled: boolean

      // 管理措施
      managementTypes: {
        planting: boolean        // 人工种植
        weeding: boolean         // 除草
        fertilization: boolean   // 施肥
        irrigation: boolean      // 灌溉
      }
    }
  }
}
```

### 5.3 用户控制界面参数
```typescript
interface UserControlParams {
  // === 快速预设 ===
  presets: {
    'bare_terrain': {
      description: '纯净地形，无任何装饰'
      decorationEnabled: false
    }

    'sparse_natural': {
      description: '稀疏自然装饰，性能友好'
      vegetation: { density: 0.3 }
      rocks: { density: 0.2 }
    }

    'lush_ecosystem': {
      description: '茂密生态系统，丰富细节'
      vegetation: { density: 0.8 }
      rocks: { density: 0.4 }
      waterFeatures: { enabled: true }
    }

    'desert_landscape': {
      description: '沙漠景观，适应干旱环境'
      vegetation: { density: 0.1, types: ['cacti', 'succulents'] }
      rocks: { density: 0.6 }
    }

    'alpine_environment': {
      description: '高山环境，适应高海拔'
      vegetation: { density: 0.4, types: ['conifers', 'alpine_plants'] }
      rocks: { density: 0.7 }
    }
  }

  // === 随机化控制 ===
  randomization: {
    globalSeed: number           // 全局随机种子

    // 分层随机化
    layerSeeds: {
      terrain: number            // 地形随机种子
      vegetation: number         // 植被随机种子
      rocks: number              // 岩石随机种子
      water: number              // 水体随机种子
    }

    // 随机化强度
    variability: {
      spatial: number            // 空间变异性 0-1
      temporal: number           // 时间变异性 0-1
      morphological: number      // 形态变异性 0-1
    }
  }

  // === 性能控制 ===
  performance: {
    lodSettings: {
      decorationLOD: boolean     // 装饰物LOD
      maxRenderDistance: number  // 最大渲染距离
      cullingEnabled: boolean    // 视锥剔除
    }

    optimizations: {
      instancedRendering: boolean // 实例化渲染
      batchProcessing: boolean    // 批处理
      asyncGeneration: boolean    // 异步生成
    }
  }
}
```

## 6. 额外的高级参数

### 6.1 季节和时间变化
```typescript
interface SeasonalParams {
  // === 季节系统 ===
  seasons: {
    enabled: boolean

    // 季节定义
    seasonDefinition: {
      spring: { duration: number, characteristics: SeasonCharacteristics }
      summer: { duration: number, characteristics: SeasonCharacteristics }
      autumn: { duration: number, characteristics: SeasonCharacteristics }
      winter: { duration: number, characteristics: SeasonCharacteristics }
    }

    // 季节变化
    transitions: {
      gradual: boolean           // 渐变过渡
      transitionDuration: number // 过渡时长
      phenology: boolean         // 物候学变化
    }
  }

  // === 时间尺度变化 ===
  temporal: {
    // 日变化
    diurnal: {
      enabled: boolean
      lightingChanges: boolean   // 光照变化
      temperatureFluctuation: boolean // 温度波动
      humidityChanges: boolean   // 湿度变化
    }

    // 年际变化
    interannual: {
      enabled: boolean
      climateVariability: number // 气候变异性
      extremeEvents: boolean     // 极端事件
    }

    // 长期变化
    longterm: {
      enabled: boolean
      succession: boolean        // 生态演替
      geomorphicChange: boolean  // 地貌变化
      climateChange: boolean     // 气候变化
    }
  }
}
```

### 6.2 灾害和干扰参数
```typescript
interface DisturbanceParams {
  // === 自然灾害 ===
  naturalDisasters: {
    // 地质灾害
    geological: {
      earthquakes: {
        enabled: boolean
        frequency: number        // 地震频率
        magnitude: { min: number, max: number }
        faultActivity: boolean   // 断层活动
      }

      landslides: {
        enabled: boolean
        susceptibility: number   // 滑坡易发性
        triggerRainfall: number  // 触发降雨量
      }

      volcanicActivity: {
        enabled: boolean
        eruptionFrequency: number
        lavaFlows: boolean
        ashDeposition: boolean
      }
    }

    // 气象灾害
    meteorological: {
      floods: {
        enabled: boolean
        floodFrequency: number
        floodMagnitude: number
        flashFloods: boolean
      }

      droughts: {
        enabled: boolean
        droughtFrequency: number
        severity: number
        duration: number
      }

      storms: {
        enabled: boolean
        stormFrequency: number
        windSpeed: { min: number, max: number }
        hailDamage: boolean
      }
    }

    // 生物灾害
    biological: {
      fires: {
        enabled: boolean
        fireFrequency: number
        fireIntensity: number
        fireSpread: number
      }

      pestOutbreaks: {
        enabled: boolean
        outbreakFrequency: number
        affectedSpecies: string[]
      }

      diseases: {
        enabled: boolean
        diseaseSpread: number
        mortalityRate: number
      }
    }
  }
}
```

## 7. 生物群系过渡带系统 (类似七日杀)

### 7.1 生物群系定义和过渡
```typescript
interface BiomeTransitionSystem {
  // === 生物群系定义 ===
  biomes: {
    // 主要生物群系
    primaryBiomes: {
      forest: {
        temperature: { min: 10, max: 25 }
        precipitation: { min: 800, max: 2000 }
        vegetation: { density: 0.8, types: ['deciduous', 'mixed'] }
        soilType: 'forest_soil'
      }

      desert: {
        temperature: { min: 20, max: 45 }
        precipitation: { min: 0, max: 200 }
        vegetation: { density: 0.1, types: ['cacti', 'succulents'] }
        soilType: 'sandy_soil'
      }

      grassland: {
        temperature: { min: 5, max: 30 }
        precipitation: { min: 300, max: 800 }
        vegetation: { density: 0.6, types: ['grasses', 'herbs'] }
        soilType: 'prairie_soil'
      }

      tundra: {
        temperature: { min: -15, max: 5 }
        precipitation: { min: 150, max: 400 }
        vegetation: { density: 0.3, types: ['mosses', 'lichens'] }
        soilType: 'permafrost'
      }

      wetland: {
        temperature: { min: 0, max: 30 }
        precipitation: { min: 600, max: 1500 }
        vegetation: { density: 0.9, types: ['aquatic', 'marsh'] }
        soilType: 'hydric_soil'
      }

      alpine: {
        temperature: { min: -10, max: 15 }
        precipitation: { min: 400, max: 1200 }
        vegetation: { density: 0.4, types: ['alpine', 'conifers'] }
        soilType: 'rocky_soil'
      }

      // === 新增地形类型 ===
      snowlands: {
        temperature: { min: -25, max: 0 }
        precipitation: { min: 200, max: 800 }
        vegetation: { density: 0.1, types: ['evergreen', 'hardy_shrubs'] }
        soilType: 'frozen_soil'
        snowCover: { depth: { min: 10, max: 200 }, permanence: 'seasonal_to_permanent' }
        specialFeatures: ['ice_formations', 'snow_drifts', 'frozen_lakes']
      }

      urban_roads: {
        temperature: 'variable'  // 适应城市环境
        precipitation: 'managed'  // 有排水系统
        vegetation: { density: 0.05, types: ['weeds', 'street_trees'] }
        soilType: 'compacted_urban'
        surfaceType: 'paved'
        infrastructure: ['sidewalks', 'drainage', 'lighting', 'signage']
      }

      rural_roads: {
        temperature: 'ambient'   // 跟随环境温度
        precipitation: 'natural'
        vegetation: { density: 0.3, types: ['roadside_grass', 'wildflowers'] }
        soilType: 'compacted_earth'
        surfaceType: 'dirt_or_gravel'
        infrastructure: ['simple_drainage', 'occasional_signage']
      }

      muddy_terrain: {
        temperature: { min: 0, max: 30 }
        precipitation: { min: 800, max: 2000 }
        vegetation: { density: 0.6, types: ['marsh_plants', 'water_tolerant'] }
        soilType: 'waterlogged_clay'
        waterContent: 'high'
        specialFeatures: ['mud_pools', 'standing_water', 'soft_ground']
      }

      rocky_terrain: {
        temperature: { min: -5, max: 35 }
        precipitation: { min: 100, max: 1000 }
        vegetation: { density: 0.2, types: ['rock_plants', 'lichens', 'mosses'] }
        soilType: 'thin_rocky'
        rockExposure: 'high'
        specialFeatures: ['boulder_fields', 'rock_outcrops', 'scree_slopes']
      }

      volcanic_terrain: {
        temperature: { min: 10, max: 40 }
        precipitation: { min: 200, max: 1500 }
        vegetation: { density: 0.3, types: ['pioneer_plants', 'volcanic_adapted'] }
        soilType: 'volcanic_ash'
        geothermalActivity: 'present'
        specialFeatures: ['lava_flows', 'volcanic_rocks', 'hot_springs', 'fumaroles']
      }

      frozen_terrain: {
        temperature: { min: -40, max: -5 }
        precipitation: { min: 50, max: 400 }
        vegetation: { density: 0.05, types: ['arctic_plants', 'lichens'] }
        soilType: 'permafrost'
        frostDepth: 'deep'
        specialFeatures: ['ice_wedges', 'frost_heave', 'pingos', 'thermokarst']
      }

      swampy_terrain: {
        temperature: { min: 5, max: 35 }
        precipitation: { min: 1000, max: 3000 }
        vegetation: { density: 0.9, types: ['swamp_trees', 'aquatic_plants', 'moss'] }
        soilType: 'organic_muck'
        waterLevel: 'at_or_above_surface'
        specialFeatures: ['cypress_knees', 'floating_islands', 'methane_bubbles']
      }

      sandy_beaches: {
        temperature: { min: 10, max: 40 }
        precipitation: { min: 200, max: 1500 }
        vegetation: { density: 0.2, types: ['beach_grass', 'salt_tolerant'] }
        soilType: 'sand'
        salinity: 'high'
        specialFeatures: ['dunes', 'tidal_pools', 'driftwood', 'shell_deposits']
      }

      cliff_terrain: {
        temperature: { min: -10, max: 30 }
        precipitation: { min: 300, max: 1200 }
        vegetation: { density: 0.3, types: ['cliff_plants', 'hanging_vegetation'] }
        soilType: 'thin_rocky'
        slope: 'vertical_to_near_vertical'
        specialFeatures: ['rock_faces', 'ledges', 'caves', 'waterfalls']
      }
    }
  }

  // === 过渡带系统 ===
  transitionZones: {
    enabled: boolean

    // 过渡带类型
    transitionTypes: {
      // 森林-草原过渡带
      forest_grassland: {
        enabled: boolean
        width: { min: number, max: number }    // 过渡带宽度
        characteristics: {
          vegetation: {
            forestEdge: { density: 0.6, types: ['edge_trees', 'shrubs'] }
            grasslandEdge: { density: 0.4, types: ['tall_grasses', 'scattered_trees'] }
          }
          gradientType: 'linear' | 'exponential' | 'sigmoid'
        }
      }

      // 森林-沙漠过渡带 (类似七日杀)
      forest_desert: {
        enabled: boolean
        width: { min: number, max: number }
        characteristics: {
          vegetation: {
            forestSide: { density: 0.7, types: ['drought_tolerant_trees'] }
            middle: { density: 0.3, types: ['shrubs', 'sparse_grass'] }
            desertSide: { density: 0.1, types: ['desert_shrubs', 'cacti'] }
          }
          soilTransition: {
            forestSoil: 0.3
            sandySoil: 0.4
            mixedSoil: 0.3
          }
        }
      }

      // 草原-沙漠过渡带
      grassland_desert: {
        enabled: boolean
        width: { min: number, max: number }
        characteristics: {
          vegetation: {
            grasslandSide: { density: 0.5, types: ['short_grasses'] }
            middle: { density: 0.2, types: ['desert_grasses', 'shrubs'] }
            desertSide: { density: 0.1, types: ['sparse_vegetation'] }
          }
        }
      }

      // 高山-森林过渡带
      alpine_forest: {
        enabled: boolean
        width: { min: number, max: number }
        characteristics: {
          elevationGradient: boolean
          vegetation: {
            alpineSide: { density: 0.3, types: ['alpine_plants', 'dwarf_trees'] }
            middle: { density: 0.5, types: ['subalpine_conifers'] }
            forestSide: { density: 0.7, types: ['montane_forest'] }
          }
        }
      }

      // 湿地-其他群系过渡带
      wetland_transitions: {
        wetland_forest: {
          enabled: boolean
          characteristics: {
            vegetation: { types: ['riparian_forest', 'swamp_trees'] }
            soilMoisture: 'gradient'
          }
        }

        wetland_grassland: {
          enabled: boolean
          characteristics: {
            vegetation: { types: ['wet_meadows', 'sedges'] }
            seasonalFlooding: boolean
          }
        }
      }
    }

    // === 过渡带控制参数 ===
    transitionControl: {
      // 过渡模式
      transitionMode: 'sharp' | 'gradual' | 'mosaic' | 'fingered'

      // 过渡强度
      transitionIntensity: number        // 0-1, 过渡的明显程度

      // 边界复杂度
      boundaryComplexity: number         // 0-1, 边界的复杂程度

      // 微环境变化
      microhabitats: {
        enabled: boolean
        density: number                  // 微环境密度
        size: { min: number, max: number }
        types: ['springs', 'rock_outcrops', 'depressions', 'ridges']
      }

      // 干扰影响
      disturbanceEffects: {
        fire: boolean                    // 火灾对过渡带的影响
        flooding: boolean                // 洪水影响
        humanActivity: boolean           // 人类活动影响
      }
    }
  }
}
```

### 7.2 特定地形特征选择系统
```typescript
interface SpecificFeatureSelection {
  // === 湖泊类型选择 ===
  lakeTypes: {
    // 成因分类
    geneticTypes: {
      tectonicLakes: {
        enabled: boolean
        probability: number              // 出现概率 0-1
        characteristics: {
          size: 'large'
          depth: 'very_deep'
          shape: 'elongated'
          associatedFeatures: ['fault_lines', 'hot_springs']
        }
      }

      volcanicLakes: {
        enabled: boolean
        probability: number
        characteristics: {
          size: 'medium'
          depth: 'deep'
          shape: 'circular'
          associatedFeatures: ['crater_rim', 'volcanic_rocks', 'hot_springs']
        }
      }

      glacialLakes: {
        enabled: boolean
        probability: number
        characteristics: {
          size: 'variable'
          depth: 'deep'
          shape: 'irregular'
          associatedFeatures: ['moraines', 'cirques', 'u_valleys']
        }
      }

      karstLakes: {
        enabled: boolean
        probability: number
        characteristics: {
          size: 'small_to_medium'
          depth: 'variable'
          shape: 'circular_to_irregular'
          associatedFeatures: ['sinkholes', 'caves', 'limestone_terrain']
        }
      }

      oxbowLakes: {
        enabled: boolean
        probability: number
        characteristics: {
          size: 'small'
          depth: 'shallow'
          shape: 'crescent'
          associatedFeatures: ['old_river_channels', 'floodplains']
        }
      }

      pluvialLakes: {
        enabled: boolean
        probability: number
        characteristics: {
          size: 'large'
          depth: 'shallow'
          shape: 'irregular'
          associatedFeatures: ['ancient_shorelines', 'salt_deposits']
        }
      }
    }

    // 水文特征
    hydrologicalFeatures: {
      endorheicLakes: boolean            // 内流湖
      throughflowLakes: boolean          // 过流湖
      seepageLakes: boolean              // 渗流湖
      perennialLakes: boolean            // 常年湖
      seasonalLakes: boolean             // 季节湖
      ephemeralLakes: boolean            // 短暂湖
    }
  }

  // === 山地类型选择 ===
  mountainTypes: {
    // 成因分类
    geneticTypes: {
      foldMountains: {
        enabled: boolean
        probability: number
        characteristics: {
          elevation: 'high'
          slope: 'moderate_to_steep'
          ridgePattern: 'linear'
          associatedFeatures: ['anticlines', 'synclines', 'thrust_faults']
        }
      }

      faultBlockMountains: {
        enabled: boolean
        probability: number
        characteristics: {
          elevation: 'variable'
          slope: 'steep'
          ridgePattern: 'linear_to_angular'
          associatedFeatures: ['fault_scarps', 'grabens', 'horsts']
        }
      }

      volcanicMountains: {
        enabled: boolean
        probability: number
        characteristics: {
          elevation: 'high'
          slope: 'steep'
          ridgePattern: 'radial'
          associatedFeatures: ['craters', 'lava_flows', 'pyroclastic_deposits']
        }
      }

      domeMountains: {
        enabled: boolean
        probability: number
        characteristics: {
          elevation: 'moderate'
          slope: 'gentle_to_moderate'
          ridgePattern: 'circular'
          associatedFeatures: ['intrusive_rocks', 'radial_drainage']
        }
      }

      erosionalMountains: {
        enabled: boolean
        probability: number
        characteristics: {
          elevation: 'moderate'
          slope: 'variable'
          ridgePattern: 'dendritic'
          associatedFeatures: ['deep_valleys', 'resistant_rocks']
        }
      }
    }
  }

  // === 河流类型选择 ===
  riverTypes: {
    // 流域特征
    drainagePatterns: {
      dendriticPattern: {
        enabled: boolean
        probability: number
        suitableFor: ['uniform_geology', 'gentle_slopes']
      }

      trellisPattern: {
        enabled: boolean
        probability: number
        suitableFor: ['folded_rocks', 'alternating_hard_soft_layers']
      }

      radialPattern: {
        enabled: boolean
        probability: number
        suitableFor: ['volcanic_cones', 'domes']
      }

      rectangularPattern: {
        enabled: boolean
        probability: number
        suitableFor: ['jointed_rocks', 'fault_systems']
      }

      parallelPattern: {
        enabled: boolean
        probability: number
        suitableFor: ['uniform_slope', 'parallel_ridges']
      }
    }

    // 河道类型
    channelTypes: {
      straightChannels: {
        enabled: boolean
        probability: number
        characteristics: ['steep_gradient', 'resistant_banks']
      }

      meanderingChannels: {
        enabled: boolean
        probability: number
        characteristics: ['gentle_gradient', 'cohesive_banks']
      }

      braidedChannels: {
        enabled: boolean
        probability: number
        characteristics: ['high_sediment_load', 'variable_discharge']
      }

      anastomosing: {
        enabled: boolean
        probability: number
        characteristics: ['low_gradient', 'stable_banks', 'fine_sediments']
      }
    }
  }

  // === 特殊地貌选择 ===
  specialLandforms: {
    // 喀斯特地貌
    karstFeatures: {
      enabled: boolean

      features: {
        sinkholes: {
          enabled: boolean
          density: number
          size: { min: number, max: number }
        }

        caves: {
          enabled: boolean
          complexity: 'simple' | 'moderate' | 'complex'
          depth: { min: number, max: number }
        }

        springs: {
          enabled: boolean
          flowRate: { min: number, max: number }
          temperature: 'cold' | 'warm' | 'hot'
        }

        towers: {
          enabled: boolean              // 石林/塔状喀斯特
          height: { min: number, max: number }
          density: number
        }
      }
    }

    // 冰川地貌
    glacialFeatures: {
      enabled: boolean

      features: {
        cirques: {
          enabled: boolean
          size: { min: number, max: number }
          elevation: { min: number, max: number }
        }

        moraines: {
          enabled: boolean
          types: ['terminal', 'lateral', 'medial', 'ground']
          height: { min: number, max: number }
        }

        drumlins: {
          enabled: boolean
          orientation: number           // 冰川流向
          density: number
        }

        eskers: {
          enabled: boolean
          length: { min: number, max: number }
          sinuosity: number
        }
      }
    }

    // 风成地貌
    eolianFeatures: {
      enabled: boolean

      features: {
        sandDunes: {
          enabled: boolean
          duneTypes: ['barchan', 'linear', 'star', 'parabolic']
          height: { min: number, max: number }
          migration: boolean
        }

        yardangs: {
          enabled: boolean              // 雅丹地貌
          orientation: number           // 风向
          size: { min: number, max: number }
        }

        deflationHollows: {
          enabled: boolean
          depth: { min: number, max: number }
          size: { min: number, max: number }
        }
      }
    }

    // 海岸地貌
    coastalFeatures: {
      enabled: boolean

      features: {
        cliffs: {
          enabled: boolean
          height: { min: number, max: number }
          rockType: string[]
        }

        beaches: {
          enabled: boolean
          sedimentType: 'sand' | 'gravel' | 'cobble' | 'mixed'
          width: { min: number, max: number }
        }

        estuaries: {
          enabled: boolean
          size: { min: number, max: number }
          tidalRange: { min: number, max: number }
        }

        spits: {
          enabled: boolean
          length: { min: number, max: number }
          curvature: number
        }
      }
    }
  }
}
```

### 7.3 用户界面控制系统
```typescript
interface BiomeFeatureControlUI {
  // === 生物群系配置面板 ===
  biomeConfiguration: {
    // 主要生物群系选择
    primaryBiomes: {
      selectedBiomes: string[]         // 用户选择的生物群系
      biomeProportion: {               // 各生物群系占比
        forest: number                 // 0-1
        desert: number
        grassland: number
        tundra: number
        wetland: number
        alpine: number
      }

      // 生物群系分布模式
      distributionPattern: 'random' | 'clustered' | 'zoned' | 'gradient' | 'custom'

      // 生物群系大小控制
      biomeSize: {
        minSize: number                // 最小生物群系面积
        maxSize: number                // 最大生物群系面积
        fragmentationLevel: number     // 破碎化程度 0-1
      }
    }

    // 过渡带配置
    transitionConfiguration: {
      enableTransitions: boolean

      // 全局过渡设置
      globalTransitionSettings: {
        defaultWidth: number           // 默认过渡带宽度
        transitionSharpness: number    // 过渡锐度 0-1
        boundaryComplexity: number     // 边界复杂度 0-1
      }

      // 特定过渡带设置
      specificTransitions: {
        forest_desert: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        forest_grassland: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        grassland_desert: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        alpine_forest: {
          enabled: boolean
          width: number
          elevationDriven: boolean     // 是否由海拔驱动
        }

        wetland_others: {
          enabled: boolean
          width: number
          seasonalVariation: boolean   // 季节性变化
        }

        // === 新增过渡带类型 ===
        forest_snowlands: {
          enabled: boolean
          width: number
          elevationDriven: boolean     // 海拔驱动的雪线过渡
          seasonalVariation: boolean   // 季节性雪线变化
        }

        grassland_muddy: {
          enabled: boolean
          width: number
          moistureDriven: boolean      // 湿度驱动过渡
          seasonalFlooding: boolean    // 季节性洪水影响
        }

        rocky_vegetation: {
          enabled: boolean
          width: number
          soilDepthGradient: boolean   // 土壤深度梯度
        }

        volcanic_normal: {
          enabled: boolean
          width: number
          ageGradient: boolean         // 火山活动年代梯度
          vegetationSuccession: boolean // 植被演替
        }

        coastal_inland: {
          enabled: boolean
          width: number
          salinityGradient: boolean    // 盐度梯度
          elevationGradient: boolean   // 海拔梯度
        }

        frozen_temperate: {
          enabled: boolean
          width: number
          temperatureGradient: boolean // 温度梯度
          permafrostDepth: boolean     // 永冻土深度变化
        }

        swamp_dryland: {
          enabled: boolean
          width: number
          drainageGradient: boolean    // 排水梯度
          hydricSoils: boolean         // 湿润土壤过渡
        }

        urban_rural: {
          enabled: boolean
          width: number
          developmentDensity: boolean  // 开发密度梯度
          infrastructureGradient: boolean // 基础设施梯度
        }
      }
    }
  }

  // === 特征选择面板 ===
  featureSelectionPanel: {
    // 湖泊特征选择
    lakeFeatures: {
      // 成因类型选择器
      geneticTypeSelector: {
        tectonicLakes: {
          enabled: boolean
          probability: number          // 滑块 0-1
          minCount: number
          maxCount: number
        }

        volcanicLakes: {
          enabled: boolean
          probability: number
          requiresVolcanicTerrain: boolean // 是否需要火山地形
        }

        glacialLakes: {
          enabled: boolean
          probability: number
          requiresHighElevation: boolean   // 是否需要高海拔
        }

        karstLakes: {
          enabled: boolean
          probability: number
          requiresLimestone: boolean       // 是否需要石灰岩地形
        }

        oxbowLakes: {
          enabled: boolean
          probability: number
          requiresRivers: boolean          // 是否需要河流系统
        }
      }

      // 湖泊特征组合
      featureCombinations: {
        allowMultipleTypes: boolean        // 允许多种类型共存
        dominantType: string               // 主导类型
        rareTypeChance: number             // 稀有类型出现概率
      }
    }

    // 山地特征选择
    mountainFeatures: {
      geneticTypeSelector: {
        foldMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            anticlines: boolean
            synclines: boolean
            thrustFaults: boolean
          }
        }

        faultBlockMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            faultScarps: boolean
            grabens: boolean
            horsts: boolean
          }
        }

        volcanicMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            craters: boolean
            lavaFlows: boolean
            pyroclasticDeposits: boolean
          }
        }

        domeMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            intrusiveRocks: boolean
            radialDrainage: boolean
          }
        }
      }
    }

    // 特殊地貌选择
    specialLandforms: {
      karstFeatures: {
        enabled: boolean
        featureTypes: {
          sinkholes: { enabled: boolean, density: number }
          caves: { enabled: boolean, complexity: string }
          springs: { enabled: boolean, temperature: string }
          towers: { enabled: boolean, density: number }
        }
      }

      glacialFeatures: {
        enabled: boolean
        featureTypes: {
          cirques: { enabled: boolean, count: number }
          moraines: { enabled: boolean, types: string[] }
          drumlins: { enabled: boolean, density: number }
          eskers: { enabled: boolean, length: number }
        }
      }

      eolianFeatures: {
        enabled: boolean
        featureTypes: {
          sandDunes: { enabled: boolean, types: string[] }
          yardangs: { enabled: boolean, orientation: number }
          deflationHollows: { enabled: boolean, density: number }
        }
      }

      coastalFeatures: {
        enabled: boolean
        featureTypes: {
          cliffs: { enabled: boolean, height: number }
          beaches: { enabled: boolean, sedimentType: string }
          estuaries: { enabled: boolean, size: number }
          spits: { enabled: boolean, length: number }
        }
      }
    }
  }
}
```

### 7.3 用户界面控制系统
```typescript
interface BiomeFeatureControlUI {
  // === 生物群系配置面板 ===
  biomeConfiguration: {
    // 主要生物群系选择
    primaryBiomes: {
      selectedBiomes: string[]         // 用户选择的生物群系
      biomeProportion: {               // 各生物群系占比
        forest: number                 // 0-1
        desert: number
        grassland: number
        tundra: number
        wetland: number
        alpine: number
      }

      // 生物群系分布模式
      distributionPattern: 'random' | 'clustered' | 'zoned' | 'gradient' | 'custom'

      // 生物群系大小控制
      biomeSize: {
        minSize: number                // 最小生物群系面积
        maxSize: number                // 最大生物群系面积
        fragmentationLevel: number     // 破碎化程度 0-1
      }
    }

    // 过渡带配置
    transitionConfiguration: {
      enableTransitions: boolean

      // 全局过渡设置
      globalTransitionSettings: {
        defaultWidth: number           // 默认过渡带宽度
        transitionSharpness: number    // 过渡锐度 0-1
        boundaryComplexity: number     // 边界复杂度 0-1
      }

      // 特定过渡带设置
      specificTransitions: {
        forest_desert: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        forest_grassland: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        grassland_desert: {
          enabled: boolean
          width: number
          characteristics: 'gradual' | 'abrupt' | 'mosaic'
        }

        alpine_forest: {
          enabled: boolean
          width: number
          elevationDriven: boolean     // 是否由海拔驱动
        }

        wetland_others: {
          enabled: boolean
          width: number
          seasonalVariation: boolean   // 季节性变化
        }
      }
    }
  }

  // === 特征选择面板 ===
  featureSelectionPanel: {
    // 湖泊特征选择
    lakeFeatures: {
      // 成因类型选择器
      geneticTypeSelector: {
        tectonicLakes: {
          enabled: boolean
          probability: number          // 滑块 0-1
          minCount: number
          maxCount: number
        }

        volcanicLakes: {
          enabled: boolean
          probability: number
          requiresVolcanicTerrain: boolean // 是否需要火山地形
        }

        glacialLakes: {
          enabled: boolean
          probability: number
          requiresHighElevation: boolean   // 是否需要高海拔
        }

        karstLakes: {
          enabled: boolean
          probability: number
          requiresLimestone: boolean       // 是否需要石灰岩地形
        }

        oxbowLakes: {
          enabled: boolean
          probability: number
          requiresRivers: boolean          // 是否需要河流系统
        }
      }

      // 湖泊特征组合
      featureCombinations: {
        allowMultipleTypes: boolean        // 允许多种类型共存
        dominantType: string               // 主导类型
        rareTypeChance: number             // 稀有类型出现概率
      }
    }

    // 山地特征选择
    mountainFeatures: {
      geneticTypeSelector: {
        foldMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            anticlines: boolean
            synclines: boolean
            thrustFaults: boolean
          }
        }

        faultBlockMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            faultScarps: boolean
            grabens: boolean
            horsts: boolean
          }
        }

        volcanicMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            craters: boolean
            lavaFlows: boolean
            pyroclasticDeposits: boolean
          }
        }

        domeMountains: {
          enabled: boolean
          probability: number
          associatedFeatures: {
            intrusiveRocks: boolean
            radialDrainage: boolean
          }
        }
      }
    }

    // 特殊地貌选择
    specialLandforms: {
      karstFeatures: {
        enabled: boolean
        featureTypes: {
          sinkholes: { enabled: boolean, density: number }
          caves: { enabled: boolean, complexity: string }
          springs: { enabled: boolean, temperature: string }
          towers: { enabled: boolean, density: number }
        }
      }

      glacialFeatures: {
        enabled: boolean
        featureTypes: {
          cirques: { enabled: boolean, count: number }
          moraines: { enabled: boolean, types: string[] }
          drumlins: { enabled: boolean, density: number }
          eskers: { enabled: boolean, length: number }
        }
      }

      eolianFeatures: {
        enabled: boolean
        featureTypes: {
          sandDunes: { enabled: boolean, types: string[] }
          yardangs: { enabled: boolean, orientation: number }
          deflationHollows: { enabled: boolean, density: number }
        }
      }

      coastalFeatures: {
        enabled: boolean
        featureTypes: {
          cliffs: { enabled: boolean, height: number }
          beaches: { enabled: boolean, sedimentType: string }
          estuaries: { enabled: boolean, size: number }
          spits: { enabled: boolean, length: number }
        }
      }
    }
  }

  // === 智能建议系统 ===
  intelligentSuggestions: {
    // 地理一致性检查
    geographicConsistency: {
      enabled: boolean

      // 自动建议
      autoSuggestions: {
        climaticConsistency: boolean     // 气候一致性建议
        geologicConsistency: boolean     // 地质一致性建议
        hydrologicConsistency: boolean   // 水文一致性建议
      }

      // 冲突检测
      conflictDetection: {
        enabled: boolean
        warningLevel: 'info' | 'warning' | 'error'
        autoResolve: boolean             // 自动解决冲突
      }
    }

    // 真实性评估
    realismAssessment: {
      enabled: boolean

      // 评估指标
      assessmentCriteria: {
        geologicRealism: boolean         // 地质真实性
        climaticRealism: boolean         // 气候真实性
        ecologicRealism: boolean         // 生态真实性
        hydrologicRealism: boolean       // 水文真实性
      }

      // 评分系统
      scoringSystem: {
        overallScore: number             // 总体评分 0-100
        categoryScores: {
          geology: number
          climate: number
          ecology: number
          hydrology: number
        }
      }
    }

    // 优化建议
    optimizationSuggestions: {
      enabled: boolean

      // 建议类型
      suggestionTypes: {
        performanceOptimization: boolean  // 性能优化建议
        visualEnhancement: boolean        // 视觉增强建议
        gameplayImprovement: boolean      // 游戏性改进建议
        realismImprovement: boolean       // 真实性改进建议
      }
    }
  }
}
```

### 7.4 预设配置系统
```typescript
interface PresetConfigurationSystem {
  // === 地理主题预设 ===
  geographicPresets: {
    'temperate_mixed': {
      description: '温带混合地形，森林、草原、湖泊并存'
      biomes: {
        forest: 0.4,
        grassland: 0.3,
        wetland: 0.2,
        alpine: 0.1
        snowlands: 0.0
        urban_roads: 0.0
        rural_roads: 0.0
        muddy_terrain: 0.0
        rocky_terrain: 0.0
        volcanic_terrain: 0.0
        frozen_terrain: 0.0
        swampy_terrain: 0.0
        sandy_beaches: 0.0
        cliff_terrain: 0.0
      }
      transitions: {
        enabled: true,
        width: 'moderate',
        complexity: 'medium'
      }
      features: {
        lakes: { glacial: true, oxbow: true },
        mountains: { fold: true, erosional: true },
        rivers: { dendritic: true, meandering: true }
      }
    }

    'arid_landscape': {
      description: '干旱景观，沙漠为主，少量绿洲'
      biomes: {
        desert: 0.7,
        grassland: 0.2,
        wetland: 0.1
      }
      transitions: {
        enabled: true,
        width: 'narrow',
        complexity: 'high'
      }
      features: {
        lakes: { pluvial: true, seasonal: true },
        mountains: { fault_block: true },
        specialLandforms: { eolian: true }
      }
    }

    'alpine_region': {
      description: '高山地区，垂直分带明显'
      biomes: {
        alpine: 0.4,
        forest: 0.3,
        tundra: 0.2,
        wetland: 0.1
      }
      transitions: {
        enabled: true,
        width: 'wide',
        elevationDriven: true
      }
      features: {
        lakes: { glacial: true, tarn: true },
        mountains: { fold: true, volcanic: true },
        specialLandforms: { glacial: true }
      }
    }

    'coastal_region': {
      description: '海岸地区，多样化地形'
      biomes: {
        forest: 0.3,
        grassland: 0.3,
        wetland: 0.4
      }
      transitions: {
        enabled: true,
        width: 'variable',
        tidalInfluence: true
      }
      features: {
        lakes: { coastal: true, brackish: true },
        specialLandforms: { coastal: true },
        rivers: { estuarine: true }
      }
    }

    'karst_landscape': {
      description: '喀斯特地貌，溶洞、石林丰富'
      biomes: {
        forest: 0.5,
        grassland: 0.3,
        wetland: 0.2
      }
      features: {
        lakes: { karst: true, underground: true },
        specialLandforms: { karst: true },
        rivers: { underground: true, springs: true }
      }
    }

    // === 新增地理主题预设 ===
    'arctic_tundra': {
      description: '北极苔原，永冻土和雪地为主'
      biomes: {
        frozen_terrain: 0.4,
        snowlands: 0.3,
        tundra: 0.3
      }
      transitions: {
        enabled: true,
        width: 'wide',
        temperatureDriven: true
      }
      features: {
        lakes: { glacial: true, frozen: true },
        specialLandforms: { permafrost: true, ice_formations: true }
      }
    }

    'volcanic_islands': {
      description: '火山岛屿，多样化火山地貌'
      biomes: {
        volcanic_terrain: 0.4,
        rocky_terrain: 0.2,
        sandy_beaches: 0.2,
        forest: 0.2
      }
      transitions: {
        enabled: true,
        width: 'narrow',
        elevationDriven: true
      }
      features: {
        lakes: { volcanic: true, crater: true },
        mountains: { volcanic: true },
        specialLandforms: { lava_flows: true, hot_springs: true }
      }
    }

    'swamp_wetlands': {
      description: '沼泽湿地，水域和泥泞地形丰富'
      biomes: {
        swampy_terrain: 0.5,
        muddy_terrain: 0.3,
        wetland: 0.2
      }
      transitions: {
        enabled: true,
        width: 'gradual',
        moistureDriven: true
      }
      features: {
        lakes: { swamp: true, shallow: true },
        rivers: { meandering: true, slow_flow: true },
        specialFeatures: { methane_seeps: true, floating_islands: true }
      }
    }

    'cliff_coastline': {
      description: '悬崖海岸，陡峭地形和海滩并存'
      biomes: {
        cliff_terrain: 0.4,
        sandy_beaches: 0.3,
        rocky_terrain: 0.2,
        grassland: 0.1
      }
      transitions: {
        enabled: true,
        width: 'sharp',
        elevationDriven: true
      }
      features: {
        specialLandforms: { sea_cliffs: true, sea_caves: true, beaches: true },
        waterFeatures: { tidal_pools: true, coastal_springs: true }
      }
    }

    'urban_suburban': {
      description: '城市郊区，道路网络和建筑用地'
      biomes: {
        urban_roads: 0.3,
        rural_roads: 0.2,
        grassland: 0.3,
        forest: 0.2
      }
      transitions: {
        enabled: true,
        width: 'variable',
        developmentDriven: true
      }
      features: {
        infrastructure: { road_networks: true, drainage_systems: true },
        landUse: { residential: true, commercial: true, parks: true }
      }
    }

    'badlands_desert': {
      description: '荒地沙漠，干旱和侵蚀地貌'
      biomes: {
        desert: 0.4,
        badlands: 0.3,
        rocky_terrain: 0.2,
        muddy_terrain: 0.1
      }
      transitions: {
        enabled: true,
        width: 'sharp',
        erosionDriven: true
      }
      features: {
        specialLandforms: { arroyos: true, mesas: true, hoodoos: true },
        erosionFeatures: { gullies: true, flash_flood_channels: true }
      }
    }
  }

  // === 游戏风格预设 ===
  gameStylePresets: {
    'survival_challenge': {
      description: '生存挑战，资源稀缺，地形复杂'
      characteristics: {
        resourceScarcity: 'high',
        terrainDifficulty: 'extreme',
        weatherHarshness: 'severe'
      }
      biomes: {
        desert: 0.3,
        tundra: 0.3,
        alpine: 0.2,
        forest: 0.2
      }
      features: {
        waterSources: 'rare',
        shelterSites: 'limited',
        dangerousAreas: 'frequent'
      }
    }

    'exploration_adventure': {
      description: '探索冒险，地形多样，景观丰富'
      characteristics: {
        terrainVariety: 'maximum',
        landmarkDensity: 'high',
        hiddenFeatures: 'abundant'
      }
      biomes: 'all_enabled',
      features: {
        caves: 'extensive',
        ruins: 'scattered',
        uniqueLandforms: 'frequent'
      }
    }

    'peaceful_building': {
      description: '和平建造，地形平缓，资源丰富'
      characteristics: {
        terrainGentleness: 'high',
        resourceAbundance: 'rich',
        buildingSuitability: 'excellent'
      }
      biomes: {
        forest: 0.4,
        grassland: 0.4,
        wetland: 0.2
      }
      features: {
        flatAreas: 'abundant',
        waterAccess: 'convenient',
        materialSources: 'nearby'
      }
    }
  }
}
```

## 8. 新增地形类型详细参数

### 8.1 雪地地形 (Snowlands)
```typescript
interface SnowlandsParams {
  // === 雪覆盖参数 ===
  snowCoverage: {
    depth: { min: number, max: number }        // 积雪深度 cm
    density: number                            // 雪密度 0-1
    permanence: 'temporary' | 'seasonal' | 'permanent'

    // 雪的类型
    snowType: {
      powderSnow: boolean                      // 粉雪
      packedSnow: boolean                      // 压实雪
      crustedSnow: boolean                     // 结壳雪
      granularSnow: boolean                    // 粒雪
    }

    // 雪分布模式
    distribution: {
      uniform: boolean                         // 均匀分布
      drifted: boolean                         // 风吹堆积
      shadowed: boolean                        // 阴影区域
      elevationDependent: boolean              // 海拔相关
    }
  }

  // === 冰层特征 ===
  iceFeatures: {
    frozenLakes: {
      enabled: boolean
      iceThickness: { min: number, max: number }
      transparency: number                     // 冰层透明度
      surfaceTexture: 'smooth' | 'rough' | 'cracked'
    }

    icicles: {
      enabled: boolean
      density: number
      size: { min: number, max: number }
      locations: ['cliffs', 'overhangs', 'buildings']
    }

    frostFormations: {
      enabled: boolean
      types: ['hoarfrost', 'rime_ice', 'glaze_ice']
      coverage: number                         // 覆盖率 0-1
    }
  }

  // === 雪地植被 ===
  snowVegetation: {
    evergreenTrees: {
      density: number
      snowLoad: boolean                        // 树枝积雪
      types: ['spruce', 'fir', 'pine']
    }

    winterShrubs: {
      density: number
      dormancy: boolean                        // 休眠状态
      types: ['willow', 'birch', 'juniper']
    }

    groundCover: {
      mossesLichens: boolean                   // 苔藓地衣
      winterGrasses: boolean                   // 冬季草类
      visibility: 'buried' | 'partially_visible' | 'exposed'
    }
  }
}
```

### 8.2 道路系统 (Road Systems)
```typescript
interface RoadSystemParams {
  // === 城镇道路 (Urban Roads) ===
  urbanRoads: {
    // 道路网络
    roadNetwork: {
      pattern: 'grid' | 'radial' | 'organic' | 'mixed'
      density: number                          // 道路密度
      connectivity: number                     // 连通性 0-1
    }

    // 道路类型
    roadTypes: {
      mainStreets: {
        width: { min: number, max: number }
        lanes: number
        surfaceMaterial: 'asphalt' | 'concrete' | 'brick' | 'stone'
        condition: 'new' | 'good' | 'fair' | 'poor'
      }

      sideStreets: {
        width: { min: number, max: number }
        surfaceMaterial: string
        parkingAllowed: boolean
      }

      alleys: {
        width: { min: number, max: number }
        surfaceMaterial: 'gravel' | 'dirt' | 'paved'
        accessibility: 'vehicle' | 'pedestrian_only'
      }
    }

    // 基础设施
    infrastructure: {
      sidewalks: {
        enabled: boolean
        width: number
        material: 'concrete' | 'brick' | 'stone'
        condition: string
      }

      streetLighting: {
        enabled: boolean
        density: number
        type: 'led' | 'fluorescent' | 'sodium' | 'gas'
      }

      drainage: {
        stormDrains: boolean
        gutters: boolean
        manholeCovers: boolean
      }

      signage: {
        streetSigns: boolean
        trafficSigns: boolean
        businessSigns: boolean
      }
    }

    // 道路状况
    roadCondition: {
      wear: number                             // 磨损程度 0-1
      cracks: boolean                          // 裂缝
      potholes: boolean                        //坑洞
      patches: boolean                         // 修补痕迹
      weathering: number                       // 风化程度
    }
  }

  // === 乡村道路 (Rural Roads) ===
  ruralRoads: {
    // 道路特征
    roadCharacteristics: {
      width: { min: number, max: number }
      surfaceType: 'dirt' | 'gravel' | 'packed_earth' | 'grass_track'
      condition: 'well_maintained' | 'moderate' | 'poor' | 'barely_passable'
    }

    // 路径模式
    pathPattern: {
      followsTopography: boolean               // 跟随地形
      straightSections: boolean                // 直线段
      windingPaths: boolean                    // 蜿蜒路径
      shortcuts: boolean                       // 捷径小径
    }

    // 路边特征
    roadsideFeatures: {
      ditches: {
        enabled: boolean
        depth: number
        vegetation: boolean
      }

      fencing: {
        enabled: boolean
        type: 'wooden' | 'wire' | 'stone' | 'hedge'
        condition: string
      }

      vegetation: {
        grassyVerges: boolean                  // 草地路肩
        wildflowers: boolean                   // 野花
        trees: boolean                         // 路边树木
        overgrowth: number                     // 植被侵占程度
      }

      utilities: {
        powerLines: boolean                    // 电线
        fencePosts: boolean                    // 围栏柱
        mailboxes: boolean                     // 邮箱
        gates: boolean                         // 大门
      }
    }
  }
}
```

### 8.3 泥泞地形 (Muddy Terrain)
```typescript
interface MuddyTerrainParams {
  // === 泥土特征 ===
  mudCharacteristics: {
    // 泥土类型
    mudType: {
      clay: boolean                            // 粘土泥
      silt: boolean                            // 淤泥
      organic: boolean                         // 有机泥
      mixed: boolean                           // 混合泥土
    }

    // 泥土状态
    consistency: {
      liquid: number                           // 液态比例 0-1
      plastic: number                          // 塑性比例
      solid: number                            // 固态比例
      viscosity: 'low' | 'medium' | 'high'    // 粘度
    }

    // 水分含量
    moisture: {
      waterContent: number                     // 含水量 0-1
      saturation: 'undersaturated' | 'saturated' | 'oversaturated'
      drainageRate: 'fast' | 'slow' | 'very_slow' | 'impermeable'
    }
  }

  // === 地表特征 ===
  surfaceFeatures: {
    // 水坑和积水
    waterPools: {
      enabled: boolean
      density: number
      size: { min: number, max: number }
      depth: { min: number, max: number }
      clarity: 'clear' | 'murky' | 'opaque'
    }

    // 泥浆区域
    mudPatches: {
      density: number
      size: { min: number, max: number }
      depth: { min: number, max: number }
      stickiness: number                       // 粘性 0-1
    }

    // 干燥区域
    dryIslands: {
      enabled: boolean
      density: number
      size: { min: number, max: number }
      elevation: number                        // 相对高度
    }

    // 表面纹理
    surfaceTexture: {
      ripples: boolean                         // 波纹
      cracks: boolean                          // 裂缝
      footprints: boolean                      // 足迹
      vehicleTracks: boolean                   // 车辙
    }
  }

  // === 植被适应 ===
  muddyVegetation: {
    // 水生植物
    aquaticPlants: {
      enabled: boolean
      types: ['cattails', 'water_lilies', 'sedges', 'rushes']
      density: number
      distribution: 'edge' | 'scattered' | 'dense_patches'
    }

    // 湿地植物
    wetlandPlants: {
      enabled: boolean
      types: ['marsh_grass', 'swamp_milkweed', 'pickerelweed']
      adaptations: ['aerial_roots', 'hollow_stems', 'waxy_leaves']
    }

    // 边缘植被
    marginalVegetation: {
      enabled: boolean
      types: ['willows', 'alders', 'ferns']
      density: number
      healthStatus: 'thriving' | 'stressed' | 'dying'
    }
  }
}
```

## 9. 实际应用示例

### 9.1 七日杀风格的过渡带生成
```typescript
interface SevenDaysToDieStyleTransition {
  // === 森林-沙漠过渡带示例 ===
  forestDesertTransition: {
    // 过渡带分区
    zones: {
      // 森林侧 (0-30% 过渡带)
      forestSide: {
        vegetation: {
          trees: { density: 0.7, types: ['oak', 'pine', 'drought_tolerant'] }
          shrubs: { density: 0.5, types: ['berry_bushes', 'thorny_shrubs'] }
          grass: { density: 0.6, types: ['forest_grass', 'dry_grass'] }
        }
        terrain: {
          soilType: 'forest_soil_to_sandy'
          rockExposure: 0.2
          waterAvailability: 0.6
        }
      }

      // 过渡中段 (30-70% 过渡带)
      middleZone: {
        vegetation: {
          trees: { density: 0.3, types: ['drought_resistant', 'dead_trees'] }
          shrubs: { density: 0.4, types: ['desert_shrubs', 'cacti'] }
          grass: { density: 0.2, types: ['sparse_grass', 'desert_grass'] }
        }
        terrain: {
          soilType: 'mixed_sandy'
          rockExposure: 0.4
          waterAvailability: 0.3
        }
      }

      // 沙漠侧 (70-100% 过渡带)
      desertSide: {
        vegetation: {
          trees: { density: 0.05, types: ['desert_trees', 'palm'] }
          shrubs: { density: 0.2, types: ['cacti', 'succulents'] }
          grass: { density: 0.05, types: ['desert_grass'] }
        }
        terrain: {
          soilType: 'sandy_desert'
          rockExposure: 0.6
          waterAvailability: 0.1
        }
      }
    }

    // 过渡效果
    transitionEffects: {
      gradualChange: true              // 渐变过渡
      microhabitats: true              // 微环境变化
      edgeEffects: true                // 边缘效应
      seasonalVariation: true          // 季节性变化
    }
  }
}
```

### 8.2 特定地形特征生成示例
```typescript
interface SpecificFeatureExample {
  // === 火山湖生成示例 ===
  volcanicLakeGeneration: {
    // 前置条件检查
    prerequisites: {
      volcanicTerrain: true            // 需要火山地形
      elevationRange: { min: 500, max: 3000 }  // 海拔范围
      slopeConstraint: { max: 30 }     // 最大坡度限制
    }

    // 生成参数
    generationParams: {
      craterSize: { min: 50, max: 500 }        // 火山口大小
      lakeDepth: { min: 10, max: 200 }         // 湖泊深度
      rimHeight: { min: 5, max: 50 }           // 火山口边缘高度

      // 关联特征
      associatedFeatures: {
        hotSprings: { probability: 0.7, count: { min: 1, max: 3 } }
        volcanicRocks: { density: 0.8, distribution: 'radial' }
        mineralDeposits: { probability: 0.5, types: ['sulfur', 'obsidian'] }
      }
    }

    // 生成算法
    algorithm: {
      step1: 'identify_suitable_locations'     // 识别合适位置
      step2: 'create_crater_depression'        // 创建火山口凹陷
      step3: 'add_water_body'                  // 添加水体
      step4: 'generate_rim_structure'          // 生成边缘结构
      step5: 'place_associated_features'       // 放置关联特征
      step6: 'apply_weathering_effects'        // 应用风化效果
    }
  }

  // === 喀斯特地貌生成示例 ===
  karstLandscapeGeneration: {
    // 前置条件
    prerequisites: {
      rockType: 'limestone'            // 石灰岩地质
      precipitation: { min: 600 }      // 最小降水量
      temperature: { min: 5 }          // 最低温度
    }

    // 特征组合
    featureCombination: {
      // 地表特征
      surfaceFeatures: {
        sinkholes: { density: 0.3, size: { min: 5, max: 100 } }
        towers: { density: 0.1, height: { min: 10, max: 200 } }
        poljes: { count: { min: 0, max: 2 }, size: { min: 100, max: 1000 } }
      }

      // 地下特征
      undergroundFeatures: {
        caves: { complexity: 'high', depth: { min: 5, max: 100 } }
        undergroundRivers: { probability: 0.6 }
        stalactites: { density: 0.8 }
      }

      // 水文特征
      hydrologicalFeatures: {
        springs: { count: { min: 2, max: 8 }, flowRate: 'variable' }
        disappearingStreams: { probability: 0.7 }
        karstLakes: { count: { min: 1, max: 3 } }
      }
    }
  }
}
```

---

## 总结

这个扩展的地形生成系统提供了：

### 1. **生物群系过渡带系统**
- ✅ **真实过渡**: 类似七日杀的自然过渡效果
- ✅ **多种模式**: 渐变、马赛克、指状等过渡模式
- ✅ **智能控制**: 基于环境因子的过渡带生成

### 2. **特定地形特征选择**
- ✅ **成因分类**: 按地质成因选择湖泊、山地类型
- ✅ **概率控制**: 用户可设置各种特征的出现概率
- ✅ **关联特征**: 自动生成相关的地质特征

### 3. **用户友好界面**
- ✅ **直观选择**: 复选框+滑块的简单控制
- ✅ **智能建议**: 地理一致性检查和优化建议
- ✅ **预设系统**: 从简单到复杂的多种预设

### 4. **核心优势**
- **真实性**: 基于真实地理过程的生成算法
- **可控性**: 用户可精确控制每种地形特征
- **多样性**: 支持全球各种地形类型和组合
- **游戏性**: 针对不同游戏风格的优化预设

这样的设计让用户可以创造出既真实又符合游戏需求的复杂地形系统！
