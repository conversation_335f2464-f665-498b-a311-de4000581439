# 建筑生成器详细规格文档

## 1. 概述

建筑生成器是体素编辑器的核心组件，负责根据用户设定的参数自动生成各种类型的建筑结构。系统采用参数化设计，支持高度自定义的建筑生成，同时集成方块类型系统以支持游戏机制。

## 2. 体素尺寸系统

### 2.1 尺寸定义
```typescript
interface VoxelScale {
  name: string
  size: number          // 厘米为单位
  usage: string[]
  precision: 'rough' | 'normal' | 'fine' | 'ultra_fine'
  gameScale: number     // 游戏中的相对尺寸
}

const VoxelScales = {
  macro: { 
    name: '巨型方块', 
    size: 500, 
    usage: ['大型地形', '城市规划'], 
    precision: 'rough',
    gameScale: 5.0
  },
  large: { 
    name: '大方块', 
    size: 100, 
    usage: ['建筑主体', '大型结构'], 
    precision: 'normal',
    gameScale: 1.0
  },
  medium: { 
    name: '中方块', 
    size: 50, 
    usage: ['建筑细节', '装饰元素'], 
    precision: 'normal',
    gameScale: 0.5
  },
  small: { 
    name: '小方块', 
    size: 20, 
    usage: ['精细装饰', '小型构件'], 
    precision: 'fine',
    gameScale: 0.2
  },
  micro: { 
    name: '微型方块', 
    size: 5, 
    usage: ['纹理细节', '超精细装饰'], 
    precision: 'ultra_fine',
    gameScale: 0.05
  }
}
```

### 2.2 尺寸约束
- **最小建筑尺寸**: 3×3×3 方块
- **最大建筑尺寸**: 128×128×64 方块
- **推荐尺寸范围**: 8×8×6 到 64×64×32 方块

## 3. 方块类型系统

### 3.1 方块属性定义
```typescript
interface VoxelBlockType {
  // 基础信息
  id: string
  name: string
  description: string
  category: 'structural' | 'decorative' | 'functional' | 'special'
  
  // 游戏属性
  defense: number           // 防御力 (0-100)
  health: number           // 生命值/耐久度 (1-1000)
  hardness: number         // 硬度 (1-10, 影响破坏时间)
  weight: number           // 重量 (影响建造速度)
  
  // 物理属性
  density: number          // 密度 (kg/m³)
  flammable: boolean       // 是否可燃
  explosive: boolean       // 是否易爆
  waterproof: boolean      // 是否防水
  corrosionResistant: boolean // 是否抗腐蚀
  
  // 环境属性
  thermalConductivity: number    // 导热性 (0-1)
  electricalConductivity: number // 导电性 (0-1)
  soundInsulation: number        // 隔音性 (0-1)
  lightTransmission: number      // 透光性 (0-1)
  
  // 视觉属性
  material: MaterialID
  baseColor: Color
  texture: string
  normalMap?: string
  roughnessMap?: string
  metallicMap?: string
  
  // 经济属性
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  baseCost: number         // 基础成本
  maintenanceCost: number  // 维护成本
  
  // 特殊效果
  specialEffects: SpecialEffect[]
  craftingRequirements: CraftingRequirement[]
}
```

### 3.2 预设方块类型

#### 木质系列
```typescript
const WoodBlocks = {
  pine_wood: {
    defense: 15, health: 80, flammable: true, baseCost: 1,
    thermalConductivity: 0.3, soundInsulation: 0.6
  },
  oak_wood: {
    defense: 25, health: 120, flammable: true, baseCost: 2,
    thermalConductivity: 0.25, soundInsulation: 0.7
  },
  ironwood: {
    defense: 40, health: 200, flammable: false, baseCost: 8,
    thermalConductivity: 0.2, soundInsulation: 0.8, rarity: 'rare'
  }
}
```

#### 石质系列
```typescript
const StoneBlocks = {
  limestone: {
    defense: 45, health: 180, waterproof: false, baseCost: 3,
    thermalConductivity: 0.8, soundInsulation: 0.4
  },
  granite: {
    defense: 60, health: 250, waterproof: true, baseCost: 5,
    thermalConductivity: 0.9, soundInsulation: 0.3
  },
  obsidian: {
    defense: 85, health: 400, explosive: false, baseCost: 20,
    thermalConductivity: 0.7, soundInsulation: 0.9, rarity: 'epic'
  }
}
```

#### 金属系列
```typescript
const MetalBlocks = {
  iron: {
    defense: 70, health: 300, electricalConductivity: 0.8, baseCost: 10,
    corrosionResistant: false, thermalConductivity: 0.95
  },
  steel: {
    defense: 85, health: 400, electricalConductivity: 0.7, baseCost: 15,
    corrosionResistant: true, thermalConductivity: 0.9
  },
  mithril: {
    defense: 95, health: 600, electricalConductivity: 0.3, baseCost: 100,
    corrosionResistant: true, thermalConductivity: 0.4, rarity: 'legendary'
  }
}
```

## 4. 建筑生成参数

### 4.1 基础分类参数
```typescript
interface BuildingGeneratorParams {
  // === 建筑类型分类 ===
  primaryType: 'residential' | 'commercial' | 'industrial' | 'religious' | 
               'military' | 'civic' | 'agricultural' | 'recreational'
  
  // 住宅细分
  residentialSubtype?: 'cottage' | 'house' | 'mansion' | 'apartment' | 
                      'villa' | 'palace' | 'hut' | 'cabin' | 'treehouse'
  
  // 商业细分
  commercialSubtype?: 'shop' | 'market' | 'inn' | 'tavern' | 'bank' | 
                     'guild_hall' | 'warehouse' | 'workshop' | 'office'
  
  // 工业细分
  industrialSubtype?: 'factory' | 'mill' | 'forge' | 'mine_entrance' | 
                     'smeltery' | 'lumber_mill' | 'power_plant'
  
  // 宗教细分
  religiousSubtype?: 'temple' | 'church' | 'monastery' | 'shrine' | 
                    'cathedral' | 'pagoda' | 'mosque' | 'synagogue'
  
  // 军事细分
  militarySubtype?: 'fortress' | 'castle' | 'tower' | 'barracks' | 
                   'armory' | 'watchtower' | 'wall_section' | 'bunker'
  
  // 市政细分
  civicSubtype?: 'town_hall' | 'library' | 'school' | 'hospital' | 
                'prison' | 'courthouse' | 'fire_station' | 'police_station'
  
  // 农业细分
  agriculturalSubtype?: 'barn' | 'silo' | 'greenhouse' | 'stable' | 
                       'chicken_coop' | 'windmill' | 'farmhouse'
  
  // 娱乐细分
  recreationalSubtype?: 'theater' | 'arena' | 'stadium' | 'park_pavilion' | 
                       'bathhouse' | 'casino' | 'museum'
}
```

### 4.2 建筑风格参数
```typescript
interface ArchitecturalStyle {
  // === 历史风格 ===
  historicalStyle: 'ancient_egyptian' | 'ancient_greek' | 'ancient_roman' |
                  'byzantine' | 'romanesque' | 'gothic' | 'renaissance' |
                  'baroque' | 'neoclassical' | 'art_nouveau' | 'art_deco'

  // === 地域风格 ===
  regionalStyle: 'european_medieval' | 'japanese_traditional' | 'chinese_classical' |
                'arabic_islamic' | 'indian_mughal' | 'african_tribal' |
                'nordic_viking' | 'russian_orthodox' | 'american_colonial'

  // === 现代风格 ===
  modernStyle: 'modernist' | 'brutalist' | 'minimalist' | 'high_tech' |
              'deconstructivist' | 'sustainable' | 'smart_building'

  // === 幻想风格 ===
  fantasyStyle: 'elven' | 'dwarven' | 'orcish' | 'steampunk' | 'cyberpunk' |
               'magical' | 'alien' | 'post_apocalyptic'

  // === 风格强度 ===
  styleIntensity: 'subtle' | 'moderate' | 'strong' | 'extreme'

  // === 混合风格 ===
  allowStyleMixing: boolean
  secondaryStyle?: string
  mixingRatio: number  // 0-1, 主风格占比
}
```

### 4.3 尺寸和规模参数
```typescript
interface BuildingDimensions {
  // === 体素尺寸控制 ===
  voxelScale: VoxelScale
  minVoxelCount: number
  maxVoxelCount: number

  // === 占地面积 ===
  footprint: {
    shape: 'rectangular' | 'square' | 'circular' | 'L_shaped' | 'T_shaped' |
           'U_shaped' | 'cross_shaped' | 'hexagonal' | 'octagonal' | 'irregular'

    dimensions: {
      width: { min: number, max: number, preferred?: number }
      depth: { min: number, max: number, preferred?: number }
      radius?: number  // 用于圆形建筑
    }

    orientation: number  // 0-360度，建筑朝向
    siteSlope: number   // 地形坡度 (-45° 到 45°)
  }

  // === 高度控制 ===
  height: {
    totalHeight: { min: number, max: number }
    floorCount: { min: number, max: number }
    floorHeight: { min: number, max: number }  // 每层高度

    // 特殊层级
    hasBasement: boolean
    basementLevels: number
    hasAttic: boolean
    hasMezzanine: boolean

    // 高度变化
    allowHeightVariation: boolean
    heightVariationRange: number  // 高度变化幅度
  }

  // === 比例控制 ===
  proportions: {
    aspectRatio: { min: number, max: number }  // 长宽比
    heightToWidthRatio: { min: number, max: number }  // 高宽比
    symmetryLevel: 'none' | 'partial' | 'bilateral' | 'radial' | 'perfect'
  }
}
```

### 4.4 结构系统参数
```typescript
interface StructuralSystem {
  // === 基础结构 ===
  foundation: {
    type: 'slab' | 'crawl_space' | 'basement' | 'pile' | 'stone_foundation'
    material: VoxelBlockType
    depth: number
    reinforcement: boolean
    drainage: boolean
  }

  // === 墙体系统 ===
  walls: {
    // 外墙
    exteriorWalls: {
      material: VoxelBlockType
      thickness: number  // 1-4 方块厚度
      insulation: boolean
      reinforcement: boolean
      weatherproofing: boolean
    }

    // 内墙
    interiorWalls: {
      material: VoxelBlockType
      thickness: number
      soundproofing: boolean
      fireproofing: boolean
    }

    // 承重墙（简化版，不做复杂计算）
    loadBearingWalls: {
      material: VoxelBlockType
      reinforcement: boolean
      distribution: 'grid' | 'perimeter' | 'central_core' | 'mixed'
    }
  }

  // === 楼板系统 ===
  floors: {
    material: VoxelBlockType
    thickness: number
    reinforcement: boolean
    insulation: boolean

    // 特殊楼板
    groundFloor: {
      material: VoxelBlockType
      moistureBarrier: boolean
      heating: boolean
    }

    upperFloors: {
      material: VoxelBlockType
      soundproofing: boolean
      vibrationDamping: boolean
    }
  }

  // === 屋顶系统 ===
  roof: {
    type: 'flat' | 'gabled' | 'hipped' | 'mansard' | 'gambrel' | 'shed' |
          'dome' | 'pyramid' | 'conical' | 'butterfly' | 'sawtooth'

    material: VoxelBlockType
    pitch: number  // 屋顶坡度 0-90度
    overhang: number  // 屋檐悬挑距离

    features: {
      gutters: boolean
      downspouts: boolean
      skylights: boolean
      chimneys: number
      dormers: number
      cupolas: boolean
    }

    // 屋顶层次
    layers: {
      structure: VoxelBlockType
      insulation: VoxelBlockType
      waterproofing: boolean
      finish: VoxelBlockType
    }
  }
}
```

### 4.5 开口系统参数
```typescript
interface OpeningSystem {
  // === 门系统 ===
  doors: {
    // 主入口
    mainEntrance: {
      count: number  // 主入口数量
      width: number  // 门宽度（方块数）
      height: number  // 门高度（方块数）
      style: 'single' | 'double' | 'sliding' | 'revolving' | 'arch' | 'ornate'
      material: VoxelBlockType

      // 入口特征
      hasSteps: boolean
      stepCount: number
      hasRamp: boolean
      hasCanopy: boolean
      hasColumns: boolean
    }

    // 次要入口
    secondaryEntrances: {
      count: number
      distribution: 'sides' | 'back' | 'random' | 'strategic'
      style: 'simple' | 'service' | 'emergency' | 'decorative'
    }

    // 内部门
    interiorDoors: {
      density: 'minimal' | 'normal' | 'high'
      style: 'simple' | 'paneled' | 'glass' | 'sliding'
      material: VoxelBlockType
    }
  }

  // === 窗户系统 ===
  windows: {
    // 窗户密度和分布
    density: 'sparse' | 'normal' | 'dense' | 'very_dense'
    distribution: 'uniform' | 'rhythmic' | 'grouped' | 'random'

    // 窗户类型
    types: {
      standard: {
        width: number
        height: number
        style: 'rectangular' | 'arched' | 'circular' | 'gothic' | 'bay'
        glazing: 'single' | 'double' | 'triple' | 'stained_glass'
      }

      specialty: {
        hasFloorToceiling: boolean
        hasBayWindows: boolean
        hasRoseWindows: boolean
        hasClerstoryWindows: boolean
      }
    }

    // 窗户特征
    features: {
      shutters: boolean
      shutterMaterial: VoxelBlockType
      windowBoxes: boolean
      awnings: boolean
      grilles: boolean
      balconettes: boolean
    }

    // 采光优化
    lightingOptimization: {
      maximizeNaturalLight: boolean
      considerOrientation: boolean
      avoidGlare: boolean
      privacyLevel: 'none' | 'partial' | 'high'
    }
  }
}
```

### 4.6 装饰和外观参数
```typescript
interface DecorationSystem {
  // === 外观装饰 ===
  exterior: {
    // 立面设计
    facade: {
      symmetry: 'perfect' | 'near_perfect' | 'asymmetric' | 'chaotic'
      texture: 'smooth' | 'rough' | 'patterned' | 'mixed' | 'weathered'
      colorScheme: 'monochrome' | 'dual_tone' | 'multi_color' | 'gradient'

      // 装饰元素
      decorativeElements: {
        cornices: boolean
        moldings: boolean
        friezes: boolean
        pilasters: boolean
        quoins: boolean  // 角石
        stringCourses: boolean  // 腰线
      }
    }

    // 柱式系统
    columns: {
      hasColumns: boolean
      style: 'doric' | 'ionic' | 'corinthian' | 'tuscan' | 'composite' |
             'simple' | 'bundled' | 'twisted' | 'fantasy'
      placement: 'entrance' | 'facade' | 'corners' | 'rhythmic' | 'random'
      material: VoxelBlockType
      proportion: 'classical' | 'elongated' | 'squat' | 'varied'
    }

    // 雕塑和装饰
    sculptures: {
      hasStatues: boolean
      hasGargoyles: boolean
      hasReliefs: boolean
      hasCarvings: boolean
      density: 'minimal' | 'moderate' | 'rich' | 'excessive'
      theme: 'religious' | 'mythological' | 'historical' | 'abstract' | 'geometric'
    }
  }

  // === 色彩系统 ===
  colorPalette: {
    scheme: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'custom'

    colors: {
      primary: Color      // 主色调
      secondary: Color    // 次色调
      accent: Color       // 强调色
      trim: Color         // 装饰边框色
      roof: Color         // 屋顶颜色
    }

    variation: {
      allowColorVariation: boolean
      variationIntensity: number  // 0-1
      weatheringEffect: boolean
      agingEffect: boolean
    }
  }

  // === 纹理系统 ===
  textureMapping: {
    wallTextures: {
      primary: string
      secondary?: string
      accent?: string
      blendMode: 'sharp' | 'gradual' | 'random'
    }

    roofTextures: {
      material: string
      pattern: 'uniform' | 'varied' | 'aged' | 'damaged'
      weathering: number  // 0-1
    }

    detailTextures: {
      doorFrames: string
      windowFrames: string
      decorativeElements: string
    }
  }
}
```

### 4.7 环境适应参数
```typescript
interface EnvironmentalAdaptation {
  // === 气候适应 ===
  climate: {
    type: 'temperate' | 'tropical' | 'arctic' | 'desert' | 'mountain' | 'coastal'

    // 温度适应
    temperature: {
      insulation: 'minimal' | 'standard' | 'heavy' | 'extreme'
      heating: boolean
      cooling: boolean
      thermalMass: boolean
    }

    // 降水适应
    precipitation: {
      roofPitch: number  // 根据降雨量调整屋顶坡度
      drainage: 'basic' | 'enhanced' | 'storm_resistant'
      waterproofing: 'standard' | 'enhanced' | 'marine_grade'
      gutterSystem: boolean
    }

    // 风力适应
    wind: {
      resistance: 'standard' | 'enhanced' | 'storm_resistant'
      orientation: 'with_wind' | 'against_wind' | 'perpendicular'
      windbreaks: boolean
      aerodynamicDesign: boolean
    }
  }

  // === 地形适应 ===
  terrain: {
    type: 'flat' | 'sloped' | 'hilly' | 'mountainous' | 'rocky' | 'swampy'

    adaptation: {
      foundationAdjustment: boolean
      terracing: boolean
      retainingWalls: boolean
      stilts: boolean  // 高脚屋
      cutAndFill: boolean  // 挖填方
    }

    stability: {
      soilType: 'stable' | 'soft' | 'rocky' | 'sandy' | 'clay'
      foundationDepth: number
      reinforcement: boolean
      drainageSystem: boolean
    }
  }

  // === 威胁等级适应 ===
  threatLevel: {
    level: 'peaceful' | 'moderate' | 'dangerous' | 'extreme' | 'warzone'

    defenses: {
      wallThickness: number
      reinforcement: boolean
      defensiveFeatures: boolean
      hiddenEntrances: boolean
      escapeRoutes: boolean
    }

    materials: {
      prioritizeDefense: boolean
      useArmoredBlocks: boolean
      fireResistance: boolean
      explosionResistance: boolean
    }
  }
}
```

### 4.8 功能空间参数
```typescript
interface FunctionalSpaces {
  // === 空间配置 ===
  spaceAllocation: {
    // 主要功能空间
    primarySpaces: {
      livingAreas: number     // 起居区域数量
      workingAreas: number    // 工作区域数量
      storageAreas: number    // 储存区域数量
      serviceAreas: number    // 服务区域数量
    }

    // 空间比例
    spaceRatios: {
      publicToPrivate: number    // 公共空间与私人空间比例
      workToLiving: number       // 工作空间与生活空间比例
      storageRatio: number       // 储存空间占比
      circulationRatio: number   // 交通空间占比
    }
  }

  // === 房间系统 ===
  rooms: {
    // 标准房间
    standardRooms: {
      count: number
      minSize: { width: number, depth: number, height: number }
      maxSize: { width: number, depth: number, height: number }
      shape: 'rectangular' | 'square' | 'L_shaped' | 'irregular'
    }

    // 特殊房间
    specialRooms: {
      hasGreatHall: boolean      // 大厅
      hasLibrary: boolean        // 图书馆
      hasWorkshop: boolean       // 工作间
      hasKitchen: boolean        // 厨房
      hasBathroom: boolean       // 浴室
      hasStorage: boolean        // 储藏室
      hasCellar: boolean         // 地窖
    }

    // 房间连接
    connectivity: {
      layout: 'linear' | 'central_hall' | 'courtyard' | 'cluster' | 'maze'
      corridorWidth: number
      hasMainHallway: boolean
      hasSecondaryHallways: boolean
      roomAccessibility: 'direct' | 'through_halls' | 'mixed'
    }
  }

  // === 垂直空间 ===
  verticalSpaces: {
    stairs: {
      type: 'straight' | 'L_shaped' | 'U_shaped' | 'spiral' | 'curved'
      width: number
      material: VoxelBlockType
      hasRailings: boolean
      hasLanding: boolean
    }

    elevators: {
      hasElevators: boolean
      elevatorCount: number
      elevatorSize: { width: number, depth: number }
    }

    atriums: {
      hasAtrium: boolean
      atriumSize: { width: number, depth: number }
      atriumHeight: number
      skylightCoverage: number
    }
  }
}
```

## 5. 智能生成算法

### 5.1 生成流程
```typescript
interface GenerationProcess {
  phases: [
    'parameter_validation',    // 参数验证
    'site_analysis',          // 场地分析
    'layout_planning',        // 布局规划
    'structure_generation',   // 结构生成
    'detail_addition',        // 细节添加
    'material_assignment',    // 材质分配
    'optimization',           // 优化处理
    'validation'              // 最终验证
  ]

  algorithms: {
    layoutAlgorithm: 'grid_based' | 'organic' | 'hybrid'
    roomPlacement: 'adjacency_matrix' | 'force_directed' | 'rule_based'
    structuralLogic: 'simplified' | 'realistic' | 'fantasy'
    detailGeneration: 'procedural' | 'template_based' | 'hybrid'
  }
}
```

### 5.2 质量控制
```typescript
interface QualityControl {
  validation: {
    structuralIntegrity: boolean    // 结构完整性检查
    spatialLogic: boolean          // 空间逻辑检查
    accessibilityCheck: boolean    // 可达性检查
    proportionCheck: boolean       // 比例检查
    styleConsistency: boolean      // 风格一致性检查
  }

  optimization: {
    voxelCount: boolean           // 体素数量优化
    materialUsage: boolean        // 材料使用优化
    performanceOptimization: boolean // 性能优化
    visualQuality: boolean        // 视觉质量优化
  }

  errorHandling: {
    fallbackStrategies: string[]  // 失败时的备选策略
    iterativeRefinement: boolean  // 迭代改进
    userFeedbackIntegration: boolean // 用户反馈集成
  }
}
```

## 6. 输出格式

### 6.1 生成结果
```typescript
interface BuildingGenerationResult {
  // 基础信息
  metadata: {
    generationId: string
    timestamp: Date
    parameters: BuildingGeneratorParams
    generationTime: number  // 毫秒
    voxelCount: number
  }

  // 建筑数据
  building: {
    voxelData: VoxelCanvas
    blockTypes: VoxelBlockType[]
    materialList: MaterialUsage[]
    structuralInfo: StructuralInfo
  }

  // 统计信息
  statistics: {
    totalCost: number
    totalDefense: number
    averageHealth: number
    materialDistribution: MaterialDistribution
    spaceUtilization: SpaceUtilization
  }

  // 质量报告
  qualityReport: {
    score: number  // 0-100
    issues: QualityIssue[]
    suggestions: string[]
    optimizationOpportunities: string[]
  }
}
```

### 6.2 导出选项
```typescript
interface ExportOptions {
  formats: ['voxel_native', 'obj', 'fbx', 'gltf', 'minecraft_schematic']
  includeMetadata: boolean
  includeMaterials: boolean
  includeAnimations: boolean
  compressionLevel: 'none' | 'low' | 'medium' | 'high'
  lodLevels: number[]  // 细节层次
}
```

---

## 总结

这个建筑生成器规格提供了：

1. **高度参数化** - 数百个可调参数
2. **智能材质系统** - 游戏机制集成的方块类型
3. **多样化风格** - 支持各种建筑风格和文化
4. **环境适应** - 根据气候和地形自动调整
5. **质量保证** - 完整的验证和优化流程
6. **灵活输出** - 多种导出格式和选项

用户可以通过简单的参数调整生成从简单小屋到复杂城堡的各种建筑，同时保持高质量和游戏机制的一致性。
```
