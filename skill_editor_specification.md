# 技能编辑器规格文档

## 1. 概述

技能编辑器专门用于创建和编辑游戏技能，采用机制分类的组合式设计理念。按技能释放方式和用途分为主动技能（9类）、被动技能（4类）和装备/道具技能（6类），每类包含20个预设模板（共380个模板）。涵盖战斗技能、角色增强技能以及装备/道具专用技能，与物品编辑器深度集成，支持多机制组合创建复杂技能，集成动画、特效、音频编辑器的资源。

## 2. 核心功能

### 2.1 技能机制分类系统
```typescript
interface SkillMechanismSystem {
  // === 主动技能 (9类，需要主动释放) ===
  activeSkills: {
    // 1. 单体矢类 (可被阻挡的投射物)
    projectile: {
      type: 'active'
      description: '发射投射物，可被其他单位阻挡'
      examples: ['火箭术', '冰箭', '毒箭', '追踪导弹', '穿甲箭']
      mechanics: {
        collision: true                    // 碰撞检测
        blockable: true                    // 可被阻挡
        projectileSpeed: number            // 飞行速度
        projectileModel: string            // 投射物模型
      }
      templates: 20                        // 每类20个模板
    }

    // 2. 单体锁定类 (无视阻挡)
    targeted: {
      type: 'active'
      description: '瞬间锁定目标，无视障碍物'
      examples: ['雷击术', '治疗术', '传送术', '定时炸弹', '魅惑术']
      mechanics: {
        collision: false                   // 无碰撞检测
        blockable: false                   // 不可阻挡
        instantHit: boolean                // 瞬间命中
        delayTime: number                  // 延迟时间
      }
      templates: 20
    }

    // 3. 弹射链类 (多目标连锁)
    chain: {
      type: 'active'
      description: '在多个目标间跳跃传递'
      examples: ['闪电链', '治疗链', '诅咒链', '分裂箭']
      mechanics: {
        maxTargets: number                 // 最大目标数
        jumpRange: number                  // 跳跃距离
        damageDecay: number                // 伤害衰减
        targetFilter: string               // 目标过滤
      }
      templates: 20
    }

    // 4. 范围瞬发类 (瞄准地面释放)
    areaInstant: {
      type: 'active'
      description: '指定地点瞬间生效'
      examples: ['火球爆炸', '冰霜新星', '地震术', '传送门']
      mechanics: {
        areaShape: 'circle' | 'rectangle' | 'sector' | 'ring' | 'line'
        areaSize: { width: number, height: number, radius: number }
        instantEffect: boolean             // 瞬间生效
        targetGround: boolean              // 目标地面
      }
      templates: 20
    }

    // 5. 范围持续类 (持续作用区域)
    areaPersistent: {
      type: 'active'
      description: '在区域内持续一段时间'
      examples: ['火墙', '冰墙', '毒雾', '治疗圈']
      mechanics: {
        areaShape: 'circle' | 'rectangle' | 'sector' | 'ring' | 'line'
        areaSize: { width: number, height: number, radius: number }
        duration: number                   // 持续时间
        tickInterval: number               // 作用间隔
      }
      templates: 20
    }

    // 6. 自身增益类 (点击直接释放)
    selfBuff: {
      type: 'active'
      description: '对施法者自身产生效果'
      examples: ['狂暴', '隐身', '护盾', '变身', '飞行']
      mechanics: {
        instantCast: boolean               // 瞬间施法
        duration: number                   // 持续时间
        stackable: boolean                 // 可叠加
        dispellable: boolean               // 可驱散
      }
      templates: 20
    }

    // 7. 放置类 (在地面放置物体)
    placement: {
      type: 'active'
      description: '在地面放置战斗物体或陷阱'
      examples: ['地雷', '冰霜陷阱', '图腾', '哨兵塔', '治疗站']
      mechanics: {
        placementRange: number             // 放置距离
        objectDuration: number             // 物体持续时间
        triggerCondition: string           // 触发条件
        maxPlacements: number              // 最大放置数量
      }
      templates: 20
    }

    // 8. 召唤类 (创建单位)
    summon: {
      type: 'active'
      description: '召唤单位或生物'
      examples: ['召唤骷髅', '召唤元素', '召唤宠物', '召唤军团']
      mechanics: {
        summonCount: number                // 召唤数量
        summonDuration: number             // 召唤持续时间
        summonUnitType: string             // 召唤单位类型
        maxSummons: number                 // 最大召唤数
      }
      templates: 20
    }

    // 9. 位移类 (改变位置)
    movement: {
      type: 'active'
      description: '改变施法者或目标的位置'
      examples: ['闪现', '冲锋', '击退', '拉拽', '跳跃', '交换位置']
      mechanics: {
        movementType: 'teleport' | 'dash' | 'push' | 'pull' | 'swap'
        movementDistance: number           // 移动距离
        movementSpeed: number              // 移动速度
        collisionDamage: boolean           // 碰撞伤害
      }
      templates: 20
    }
  }

  // === 被动技能 (4类，自动生效) ===
  passiveSkills: {
    // 11. 被动光环类 (持续影响周围)
    passiveAura: {
      type: 'passive'
      description: '以施法者为中心持续影响范围内单位，无需主动释放'
      examples: ['专注光环', '治疗光环', '护甲光环', '攻击光环', '经验光环']
      mechanics: {
        auraRadius: number                 // 光环半径
        followCaster: boolean              // 跟随施法者
        stackable: boolean                 // 可叠加
        affectAllies: boolean              // 影响友军
        affectEnemies: boolean             // 影响敌军
        alwaysActive: boolean              // 始终激活
      }
      templates: 20
    }

    // 12. 被动生存类 (环境适应和生存能力)
    passiveSurvival: {
      type: 'passive'
      description: '提供环境抗性、生存需求和感知能力'
      examples: ['抗寒', '夜视', '饱腹', '危险感知', '自然愈合']
      mechanics: {
        resistanceType: 'cold' | 'heat' | 'poison' | 'radiation' | 'disease'
        resistanceValue: number            // 抗性数值
        needsReduction: number             // 需求减少
        perceptionBonus: number            // 感知加成
        recoveryRate: number               // 恢复速度
        alwaysActive: boolean              // 始终激活
      }
      templates: 20
    }

    // 13. 被动采集类 (采集和资源相关)
    passiveGathering: {
      type: 'passive'
      description: '提升采集效率、资源产出和工具使用'
      examples: ['挖掘加速', '矿物加成', '工具耐久', '透视', '范围收割']
      mechanics: {
        gatheringType: 'mining' | 'logging' | 'harvesting' | 'fishing' | 'hunting'
        speedBonus: number                 // 速度加成
        yieldBonus: number                 // 产出加成
        toolProtection: number             // 工具保护
        specialAbility: string             // 特殊能力
        alwaysActive: boolean              // 始终激活
      }
      templates: 20
    }

    // 14. 被动战斗类 (战斗属性和能力)
    passiveCombat: {
      type: 'passive'
      description: '提升战斗属性、攻击能力和防御能力'
      examples: ['攻击加成', '暴击率', '格挡率', '反击', '吸血']
      mechanics: {
        combatType: 'attack' | 'defense' | 'critical' | 'block' | 'counter'
        bonusValue: number                 // 加成数值
        bonusPercentage: number            // 加成百分比
        triggerCondition: string           // 触发条件
        stackable: boolean                 // 可叠加
        alwaysActive: boolean              // 始终激活
      }
      templates: 20
    }
  }

  // === 装备/道具技能 (6类，专用于物品系统) ===
  itemEquipmentSkills: {
    // 15. 装备被动技能 (装备时自动激活)
    equipmentPassive: {
      type: 'equipment_passive'
      description: '装备时自动激活的被动技能，提供属性加成、抗性、特殊能力'
      examples: ['力量+10', '火焰抗性+25%', '夜视', '攻击力+50', '经验加成+20%']
      mechanics: {
        bonusType: 'flat' | 'percentage'   // 固定值或百分比
        attributeType: string              // 属性类型
        bonusValue: number                 // 加成数值
        stackable: boolean                 // 是否可叠加
        scalingWithLevel: boolean          // 是否随等级缩放
        equipmentSlotSpecific: boolean     // 是否特定装备槽位
      }
      templates: 20
    }

    // 16. 装备主动技能 (装备后可手动释放)
    equipmentActive: {
      type: 'equipment_active'
      description: '装备后可手动释放的主动技能，基于现有主动技能机制'
      examples: ['闪电链', '治疗术', '传送术', '狂暴', '护盾术']
      mechanics: {
        basedOnExistingSkills: boolean     // 基于现有技能机制
        cooldown: number                   // 冷却时间
        manaCost: number                   // 法力消耗
        charges: number                    // 使用次数 (-1为无限)
        equipmentDurabilityConsumption: number // 装备耐久消耗
      }
      templates: 20
    }

    // 17. 装备触发技能 (条件触发)
    equipmentTrigger: {
      type: 'equipment_trigger'
      description: '在特定条件下自动触发的技能'
      examples: ['攻击时10%概率释放闪电', '受伤时自动治疗', '暴击时恢复法力']
      mechanics: {
        triggerCondition: 'onAttack' | 'onDefend' | 'onCritical' | 'onLowHealth' | 'onKill'
        triggerChance: number              // 触发概率 (0-100%)
        cooldownBetweenTriggers: number    // 触发间隔
        triggerEffect: string              // 触发效果
        stackable: boolean                 // 是否可叠加
      }
      templates: 20
    }

    // 18. 道具使用技能 (使用道具时释放)
    itemUse: {
      type: 'item_use'
      description: '使用道具时释放的技能，包括消耗品和功能道具'
      examples: ['恢复生命500点', '传送到城镇', '召唤商人', '创建临时营地']
      mechanics: {
        effectType: 'instant' | 'overtime' | 'toggle' // 效果类型
        targetType: 'self' | 'target' | 'ground' | 'area' // 目标类型
        consumeItem: boolean               // 是否消耗物品
        cooldown: number                   // 冷却时间
        range: number                      // 使用范围
        environmentInteraction: boolean    // 环境交互
      }
      templates: 20
    }

    // 19. 道具被动技能 (持有时生效)
    itemPassive: {
      type: 'item_passive'
      description: '持有道具时生效的被动技能'
      examples: ['掉落率+25%', '移动速度+10%', '自动拾取', '夜视']
      mechanics: {
        requiresEquipped: boolean          // 是否需要装备才生效
        effectRadius: number               // 效果范围
        bonusType: 'flat' | 'percentage'   // 固定值或百分比
        stackable: boolean                 // 是否可叠加
        alwaysActive: boolean              // 始终激活
      }
      templates: 20
    }

    // 20. 套装技能 (套装件数激活)
    setBonus: {
      type: 'set_bonus'
      description: '达到特定套装件数时激活的技能'
      examples: ['2件套：火焰抗性+25%', '4件套：火焰伤害+30%', '6件套：火焰光环']
      mechanics: {
        requiredPieces: number             // 需要的套装件数
        setBonusType: 'attribute' | 'skill' | 'aura' | 'special' // 套装加成类型
        bonusValue: number                 // 加成数值
        stackable: boolean                 // 是否可叠加
        setId: string                      // 套装ID
      }
      templates: 20
    }
  }

  // === 范围形状详细定义 ===
  areaShapes: {
    circle: {
      parameters: { radius: number }
      description: '圆形范围'
      examples: ['火球爆炸', '治疗圈']
    }

    rectangle: {
      parameters: { width: number, height: number }
      description: '矩形范围'
      examples: ['火墙', '冰墙']
    }

    sector: {
      parameters: { radius: number, angle: number }
      description: '扇形范围'
      examples: ['顺劈斩', '龙息术', '霰弹枪']
    }

    ring: {
      parameters: { innerRadius: number, outerRadius: number }
      description: '环形范围'
      examples: ['震荡波', '排斥术']
    }

    line: {
      parameters: { length: number, width: number }
      description: '直线范围'
      examples: ['激光射线', '寒冰射线', '冲锋路径']
    }

    cone: {
      parameters: { length: number, angle: number }
      description: '锥形范围'
      examples: ['火焰喷射', '音波攻击']
    }

    cross: {
      parameters: { length: number, width: number }
      description: '十字形范围'
      examples: ['十字斩', '圣光十字']
    }

    spiral: {
      parameters: { radius: number, turns: number }
      description: '螺旋形范围'
      examples: ['旋风术', '龙卷风']
    }
  }
}
```

### 2.2 组合式技能创建系统
```typescript
interface CompositeSkillCreationSystem {
  // === 技能创建向导 ===
  skillCreationWizard: {
    // 第1步: 选择技能类型
    step1_skillType: {
      title: '选择技能类型'
      description: '选择主动技能或被动技能'

      skillTypeSelection: {
        activeSkill: {
          name: '主动技能'
          description: '需要玩家主动释放的技能'
          icon: 'active_skill_icon'
          mechanismCount: 10
          allowCombination: true           // 允许多机制组合
        }

        passiveSkill: {
          name: '被动技能'
          description: '自动生效，无需主动释放'
          icon: 'passive_skill_icon'
          mechanismCount: 4
          allowCombination: false          // 被动技能不允许组合
        }
      }
    }

    // 第2步: 选择具体机制 (主动技能可多选，被动技能单选)
    step2_mechanism: {
      title: '选择技能机制'
      description: '主动技能可选择多个机制组合，被动技能只能选择一个'

      // 主动技能机制选择
      activeMechanismSelection: {
        allowMultiple: true                // 允许多选
        conflictResolution: 'priority_order'  // 按优先级解决冲突
        maxSelections: 5                   // 最多选择5个机制

        mechanisms: [
          { id: 1, name: '单体矢类', priority: 1, type: 'active' }
          { id: 2, name: '单体锁定类', priority: 2, type: 'active' }
          { id: 3, name: '弹射链类', priority: 3, type: 'active' }
          { id: 4, name: '范围瞬发类', priority: 4, type: 'active' }
          { id: 5, name: '范围持续类', priority: 5, type: 'active' }
          { id: 6, name: '自身增益类', priority: 6, type: 'active' }
          { id: 7, name: '放置类', priority: 7, type: 'active' }
          { id: 8, name: '召唤类', priority: 8, type: 'active' }
          { id: 9, name: '位移类', priority: 9, type: 'active' }
        ]
      }

      // 被动技能机制选择
      passiveMechanismSelection: {
        allowMultiple: false               // 不允许多选
        singleChoice: true                 // 单选模式

        mechanisms: [
          { id: 11, name: '被动光环类', type: 'passive' }
          { id: 12, name: '被动生存类', type: 'passive' }
          { id: 13, name: '被动采集类', type: 'passive' }
          { id: 14, name: '被动战斗类', type: 'passive' }
        ]
      }

      // 装备/道具技能机制 (专用于物品系统)
      itemEquipmentMechanisms: {
        title: '装备/道具技能机制'
        description: '专用于物品编辑器的技能机制'
        availableIn: ['item_editor']

        mechanisms: [
          { id: 15, name: '装备被动技能', type: 'equipment_passive' }
          { id: 16, name: '装备主动技能', type: 'equipment_active' }
          { id: 17, name: '装备触发技能', type: 'equipment_trigger' }
          { id: 18, name: '道具使用技能', type: 'item_use' }
          { id: 19, name: '道具被动技能', type: 'item_passive' }
          { id: 20, name: '套装技能', type: 'set_bonus' }
        ]
      }
    }

    // 第3步: 选择具体模板
    step3_template: {
      title: '选择技能模板'
      description: '为每个选中的机制选择具体模板'

      templateSelection: {
        // 为每个选中机制显示20个模板
        perMechanism: {
          showTemplates: 20
          filterByDifficulty: boolean
          filterByGameType: boolean
          searchFunction: boolean

          // 主动技能和被动技能的不同展示
          activeSkillDisplay: {
            showCombinationPreview: boolean  // 显示组合预览
            showExecutionOrder: boolean      // 显示执行顺序
            conflictWarnings: boolean        // 冲突警告
          }

          passiveSkillDisplay: {
            showEffectDescription: boolean   // 显示效果描述
            showStackingRules: boolean       // 显示叠加规则
            showActivationConditions: boolean // 显示激活条件
          }
        }

        // 模板预览
        templatePreview: {
          show3DPreview: boolean
          showParameters: boolean
          showEffects: boolean
          playAnimation: boolean

          // 被动技能特殊预览
          passivePreview: {
            showAuraRange: boolean           // 显示光环范围
            showEffectIndicators: boolean    // 显示效果指示器
            showStatChanges: boolean         // 显示属性变化
          }
        }
      }
    }

    // 第4步: 设置伤害/效果类型 (主要针对主动技能)
    step4_effectType: {
      title: '设置效果类型'
      description: '设置技能的主要效果类型'

      // 主动技能效果类型
      activeSkillEffects: {
        damageTypes: {
          physical: '物理伤害'
          magical: '魔法伤害'
          healing: '治疗效果'
          trueDamage: '真实伤害'
          noDamage: '无伤害效果'
        }

        // 伤害分配
        damageDistribution: {
          primaryDamage: number            // 主要伤害百分比
          secondaryDamage: number          // 次要伤害百分比
          healingComponent: number         // 治疗成分百分比
        }
      }

      // 被动技能效果类型
      passiveSkillEffects: {
        effectTypes: {
          attribute_bonus: '属性加成'
          resistance: '抗性提供'
          efficiency_boost: '效率提升'
          special_ability: '特殊能力'
          aura_effect: '光环效果'
        }

        // 效果强度
        effectIntensity: {
          bonusValue: number               // 加成数值
          bonusPercentage: number          // 加成百分比
          resistanceValue: number          // 抗性数值
          efficiencyMultiplier: number     // 效率倍数
        }
      }
    }

    // 第5步: 添加特殊效果 (可多选)
    step5_specialEffects: {
      title: '添加特殊效果'
      description: '可添加多种特殊效果，主动技能效果会叠加，被动技能提供持续效果'

      // 主动技能特殊效果
      activeSkillEffects: {
        statusEffects: {
          // 控制效果
          control: ['眩晕', '冰冻', '魅惑', '恐惧', '沉默', '缠绕', '石化']

          // 伤害效果
          damage: ['燃烧', '中毒', '流血', '腐蚀', '感电', '冻伤']

          // 属性效果
          attributes: ['减速', '虚弱', '致盲', '护甲穿透', '魔法穿透']

          // 特殊效果
          special: ['吸血', '法力燃烧', '反弹', '穿透', '暴击', '击退', '拉拽']
        }

        // 效果参数
        effectParameters: {
          duration: number                 // 持续时间
          intensity: number                // 效果强度
          stackable: boolean               // 可叠加
          dispellable: boolean             // 可驱散
        }
      }

      // 被动技能特殊效果
      passiveSkillEffects: {
        permanentEffects: {
          // 属性提升
          attributeBoosts: ['攻击力', '防御力', '生命值', '法力值', '移动速度']

          // 抗性提供
          resistances: ['物理抗性', '魔法抗性', '元素抗性', '状态抗性']

          // 效率提升
          efficiencyBoosts: ['采集速度', '制作速度', '经验获取', '资源产出']

          // 特殊能力
          specialAbilities: ['夜视', '透视', '自动拾取', '快速恢复', '危险感知']
        }

        // 被动效果参数
        passiveParameters: {
          alwaysActive: boolean            // 始终激活
          stackWithOthers: boolean         // 与其他被动叠加
          requiresCondition: string        // 激活条件
          effectRadius: number             // 效果半径 (光环类)
        }
      }
    }

    // 第6步: 设置机制参数
    step6_parameters: {
      title: '设置技能参数'
      description: '为每个机制设置具体参数'

      // 主动技能参数
      activeSkillParameters: {
        // 通用参数
        commonParameters: {
          range: number                    // 施法距离
          cooldown: number                 // 冷却时间
          manaCost: number                 // 法力消耗
          castTime: number                 // 施法时间
          channelTime: number              // 引导时间
        }

        // 范围参数 (针对范围技能)
        areaParameters: {
          shape: 'circle' | 'rectangle' | 'sector' | 'ring' | 'line' | 'cone' | 'cross' | 'spiral'
          size: {
            radius: number                 // 半径
            width: number                  // 宽度
            height: number                 // 高度
            angle: number                  // 角度
            length: number                 // 长度
          }

          // 范围效果
          areaEffects: {
            damageDistribution: 'uniform' | 'center_focused' | 'edge_focused'
            falloffType: 'linear' | 'quadratic' | 'none'
            penetration: boolean           // 穿透障碍物
          }
        }

        // 投射物参数 (针对矢类技能)
        projectileParameters: {
          speed: number                    // 飞行速度
          acceleration: number             // 加速度
          homing: boolean                  // 追踪目标
          piercing: number                 // 穿透数量
          bounces: number                  // 弹跳次数
          lifetime: number                 // 存在时间
        }

        // 制造参数 (针对制造类技能)
        craftingParameters: {
          craftingTime: number             // 制作时间
          resourceCost: { [itemId: string]: number }  // 资源消耗
          toolRequirement: string          // 需要的工具
          successRate: number              // 成功率
          qualityRange: [number, number]   // 品质范围
          batchSize: number                // 批量制作数量
        }
      }

      // 被动技能参数
      passiveSkillParameters: {
        // 光环参数
        auraParameters: {
          auraRadius: number               // 光环半径
          affectAllies: boolean            // 影响友军
          affectEnemies: boolean           // 影响敌军
          stackable: boolean               // 可叠加
          followCaster: boolean            // 跟随施法者
        }

        // 生存参数
        survivalParameters: {
          resistanceValue: number          // 抗性数值
          needsReduction: number           // 需求减少百分比
          recoveryRate: number             // 恢复速度
          perceptionRange: number          // 感知范围
        }

        // 采集参数
        gatheringParameters: {
          speedBonus: number               // 速度加成百分比
          yieldBonus: number               // 产出加成百分比
          toolDurabilityBonus: number      // 工具耐久加成
          detectionRange: number           // 探测范围
        }

        // 战斗参数
        combatParameters: {
          attackBonus: number              // 攻击加成
          defenseBonus: number             // 防御加成
          criticalChance: number           // 暴击率
          blockChance: number              // 格挡率
          counterChance: number            // 反击率
        }
      }
    }

    // 第7步: 绑定视听效果
    step7_audioVisual: {
      title: '绑定视听效果'
      description: '为技能绑定动画、特效、音效'

      // 动画绑定
      animationBinding: {
        castAnimation: string              // 施法动画 (从动画编辑器)
        channelAnimation: string           // 引导动画
        projectileAnimation: string        // 投射物动画
        impactAnimation: string            // 命中动画

        // 动画参数
        animationSpeed: number             // 播放速度
        animationBlending: boolean         // 动画混合
      }

      // 特效绑定
      effectBinding: {
        castEffect: string                 // 施法特效 (从特效编辑器)
        projectileEffect: string           // 投射物特效
        impactEffect: string               // 命中特效
        areaEffect: string                 // 范围特效
        statusEffect: string               // 状态特效

        // 特效参数
        effectScale: number                // 特效缩放
        effectIntensity: number            // 特效强度
        effectDuration: number             // 特效持续时间
      }

      // 音效绑定
      audioBinding: {
        castSound: string                  // 施法音效 (从音频编辑器)
        projectileSound: string            // 投射物音效
        impactSound: string                // 命中音效
        ambientSound: string               // 环境音效

        // 音效参数
        volume: number                     // 音量
        pitch: number                      // 音调
        spatialAudio: boolean              // 3D音效
      }
    }

    // 第8步: 技能升级设置
    step8_upgrade: {
      title: '技能升级设置'
      description: '设置技能升级系统'

      // 升级基础设置
      upgradeBasics: {
        maxLevel: number                   // 最大等级 (1-10)
        initialLevel: number               // 初始等级 (0或1)
        autoUpgrade: boolean               // 自动升级
      }

      // 升级需求类型
      upgradeRequirements: {
        // 技能点升级
        skillPoints: {
          enabled: boolean
          pointsPerLevel: number           // 每级需要技能点
          totalPointsRequired: number      // 总共需要技能点
        }

        // 使用次数升级
        usageCount: {
          enabled: boolean
          usesPerLevel: number[]           // 每级需要使用次数
          resetOnDeath: boolean            // 死亡重置
        }

        // 资源升级
        resources: {
          enabled: boolean
          gold: number[]                   // 每级需要金币
          items: string[]                  // 需要物品 (从物品编辑器)
          experience: number[]             // 需要经验
        }

        // 条件升级
        conditions: {
          enabled: boolean
          unitLevel: number[]              // 单位等级要求
          prerequisiteSkills: string[]     // 前置技能
          questCompletion: string[]        // 任务完成
        }
      }

      // 升级效果
      upgradeEffects: {
        // 数值提升
        statGrowth: {
          damage: { base: number, perLevel: number }
          range: { base: number, perLevel: number }
          cooldown: { base: number, perLevel: number }
          manaCost: { base: number, perLevel: number }
          duration: { base: number, perLevel: number }
          areaSize: { base: number, perLevel: number }
        }

        // 新效果解锁
        newEffects: {
          [level: number]: string[]        // 每级解锁的新效果
        }

        // 质变升级
        qualitativeChanges: {
          [level: number]: {
            newMechanism: string           // 新增机制
            enhancedEffect: string         // 增强效果
            visualUpgrade: string          // 视觉升级
          }
        }
      }
    }

    // 第9步: 测试和调整
    step9_testing: {
      title: '测试和调整'
      description: '测试技能效果并进行调整'

      testingEnvironment: {
        testTargets: string[]              // 测试目标
        testScenarios: string[]            // 测试场景
        performanceMetrics: boolean        // 性能指标
        balanceAnalysis: boolean           // 平衡性分析

        // 被动技能特殊测试
        passiveSkillTesting: {
          effectVerification: boolean      // 效果验证
          stackingTests: boolean           // 叠加测试
          performanceImpact: boolean       // 性能影响
          balanceCheck: boolean            // 平衡性检查
        }
      }
    }
  }
}
```

### 2.3 技能模板示例库
```typescript
interface SkillTemplateExamples {
  // === 主动技能模板示例 ===
  activeSkillTemplates: {
    // 单体矢类模板示例 (20个)
    projectileTemplates: {
      // 基础矢类 (1-5)
      basic: {
        fireball: { name: '火球术', damage: 'fire', speed: 'medium', effect: 'explosion' }
        icearrow: { name: '冰箭', damage: 'ice', speed: 'fast', effect: 'slow' }
        poisondart: { name: '毒镖', damage: 'poison', speed: 'slow', effect: 'dot' }
        lightningbolt: { name: '闪电箭', damage: 'lightning', speed: 'instant', effect: 'stun' }
        holyarrow: { name: '神圣箭', damage: 'holy', speed: 'medium', effect: 'undead_bonus' }
      }

      // 特殊矢类 (6-10)
      special: {
        homingmissile: { name: '追踪导弹', tracking: true, damage: 'explosive' }
        piercingshot: { name: '穿透射击', piercing: 3, damage: 'physical' }
        splittingarrow: { name: '分裂箭', splits: 3, damage: 'physical' }
        vampiricbolt: { name: '吸血箭', lifesteal: 50, damage: 'dark' }
        manaburnshot: { name: '法力燃烧箭', manaburn: true, damage: 'arcane' }
      }

      // 高级矢类 (11-15)
      advanced: {
        chainarrow: { name: '连锁箭', bounces: 3, damage: 'lightning' }
        explosivearrow: { name: '爆炸箭', areaExplosion: true, damage: 'fire' }
        frostbolt: { name: '霜冻箭', freeze: true, damage: 'ice' }
        shadowbolt: { name: '暗影箭', ignoreArmor: true, damage: 'shadow' }
        disruptorray: { name: '瓦解射线', silences: true, damage: 'arcane' }
      }

      // 超级矢类 (16-20)
      ultimate: {
        deathlaser: { name: '死亡射线', damage: 'massive', penetratesAll: true }
        chaosorb: { name: '混沌法球', randomEffect: true, damage: 'chaos' }
        voidarrow: { name: '虚空箭', ignoresDefenses: true, damage: 'void' }
        timebolt: { name: '时间箭', delayedHit: true, damage: 'temporal' }
        soulreaper: { name: '灵魂收割', instantKill: 'lowHealth', damage: 'necrotic' }
      }
    }


  }

  // === 被动技能模板示例 ===
  passiveSkillTemplates: {
    // 被动光环类模板示例 (20个)
    passiveAuraTemplates: {
      // 属性光环 (1-5)
      attributeAuras: {
        attack_aura: { name: '攻击光环', bonus: 'attack_power', radius: 300, allies: true }
        defense_aura: { name: '防御光环', bonus: 'defense', radius: 250, allies: true }
        speed_aura: { name: '速度光环', bonus: 'movement_speed', radius: 200, allies: true }
        mana_aura: { name: '法力光环', bonus: 'mana_regen', radius: 350, allies: true }
        health_aura: { name: '生命光环', bonus: 'health_regen', radius: 300, allies: true }
      }

      // 抗性光环 (6-10)
      resistanceAuras: {
        fire_resist_aura: { name: '火抗光环', resistance: 'fire', value: 25, radius: 250 }
        ice_resist_aura: { name: '冰抗光环', resistance: 'ice', value: 25, radius: 250 }
        poison_resist_aura: { name: '毒抗光环', resistance: 'poison', value: 30, radius: 200 }
        magic_resist_aura: { name: '魔抗光环', resistance: 'magic', value: 20, radius: 300 }
        physical_resist_aura: { name: '物抗光环', resistance: 'physical', value: 15, radius: 250 }
      }

      // 效率光环 (11-15)
      efficiencyAuras: {
        crafting_aura: { name: '制作光环', bonus: 'crafting_speed', value: 50, radius: 400 }
        gathering_aura: { name: '采集光环', bonus: 'gathering_speed', value: 40, radius: 300 }
        experience_aura: { name: '经验光环', bonus: 'exp_gain', value: 25, radius: 500 }
        luck_aura: { name: '幸运光环', bonus: 'rare_find', value: 15, radius: 200 }
        repair_aura: { name: '修复光环', bonus: 'durability_loss', value: -30, radius: 250 }
      }

      // 特殊光环 (16-20)
      specialAuras: {
        fear_aura: { name: '恐惧光环', effect: 'fear', enemies: true, radius: 150 }
        intimidation_aura: { name: '威慑光环', effect: 'damage_reduction', enemies: true, radius: 200 }
        inspiration_aura: { name: '鼓舞光环', effect: 'morale_boost', allies: true, radius: 400 }
        sanctuary_aura: { name: '庇护光环', effect: 'damage_immunity', allies: true, radius: 100 }
        chaos_aura: { name: '混沌光环', effect: 'random_effects', all: true, radius: 300 }
      }
    }

    // 被动生存类模板示例 (20个)
    passiveSurvivalTemplates: {
      // 环境抗性 (1-5)
      environmentalResistance: {
        cold_resistance: { name: '抗寒', resistance: 'cold', value: 50, description: '减少寒冷伤害' }
        heat_resistance: { name: '耐热', resistance: 'heat', value: 50, description: '减少高温伤害' }
        poison_immunity: { name: '毒抗', resistance: 'poison', value: 75, description: '大幅减少中毒效果' }
        radiation_shield: { name: '辐射防护', resistance: 'radiation', value: 60, description: '减少辐射伤害' }
        disease_immunity: { name: '疾病免疫', resistance: 'disease', value: 80, description: '抵抗疾病感染' }
      }

      // 生存需求 (6-10)
      survivalNeeds: {
        slow_hunger: { name: '饱腹', effect: 'hunger_reduction', value: 40, description: '减缓饥饿速度' }
        water_conservation: { name: '节水', effect: 'thirst_reduction', value: 35, description: '减缓口渴速度' }
        energy_efficiency: { name: '节能', effect: 'fatigue_reduction', value: 30, description: '减缓疲劳速度' }
        oxygen_efficiency: { name: '节氧', effect: 'oxygen_reduction', value: 25, description: '减少氧气消耗' }
        sleep_quality: { name: '优眠', effect: 'sleep_bonus', value: 50, description: '提高睡眠效果' }
      }

      // 感知能力 (11-15)
      perceptionAbilities: {
        night_vision: { name: '夜视', ability: 'night_sight', range: 200, description: '夜晚视野更好' }
        danger_sense: { name: '危险感知', ability: 'threat_detection', range: 500, description: '提前发现威胁' }
        resource_sense: { name: '资源感知', ability: 'resource_detection', range: 300, description: '发现附近资源' }
        weather_prediction: { name: '天气预知', ability: 'weather_forecast', time: 3600, description: '预测天气变化' }
        animal_communication: { name: '动物交流', ability: 'animal_talk', range: 100, description: '与动物互动' }
      }

      // 恢复能力 (16-20)
      recoveryAbilities: {
        natural_healing: { name: '自然愈合', recovery: 'health', rate: 2, description: '缓慢恢复生命' }
        stamina_regen: { name: '体力恢复', recovery: 'stamina', rate: 150, description: '加快体力恢复' }
        mana_regen: { name: '法力恢复', recovery: 'mana', rate: 120, description: '加快法力恢复' }
        wound_resistance: { name: '抗伤', effect: 'damage_reduction', value: 15, description: '减少受到的伤害' }
        quick_recovery: { name: '快速恢复', recovery: 'all', rate: 125, description: '加快各种恢复速度' }
      }
    }
  }
}
```

## 3. 用户界面设计

### 3.1 主界面布局 (主动/被动技能分类创建)
```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 编辑 技能 工具 测试 帮助                        │
├─────────────────────────────────────────────────────────────┤
│ 工具栏: [新建] [打开] [保存] [向导] [测试] [导出]            │
├──────────┬──────────────────────────┬─────────────────────────┤
│          │                          │                         │
│ 技能分类 │     技能创建向导          │    预览面板              │
│          │                          │                         │
│ ┌主动技能┐│  ┌─────────────────────┐ │ ┌─3D预览──┐            │
│ ├1.矢类─┤│  │ 第1步: 选择技能类型   │ │ ├─效果预览─┤            │
│ ├2.锁定─┤│  │ ○主动技能 ●被动技能  │ │ ├─参数调节─┤            │
│ ├3.弹射─┤│  │                     │ │ └─兼容检查─┘            │
│ ├4.范围─┤│  │ 第2步: 选择机制       │ │                         │
│ ├5.持续─┤│  │ ☑被动光环类          │ │    属性面板              │
│ ├6.增益─┤│  │                     │ │ ┌─基础属性─┐            │
│ ├7.放置─┤│  │ 第3步: 选择模板       │ │ ├─升级设置─┤            │
│ ├8.召唤─┤│  │ [攻击光环] [防御光环] │ │ ├─资源绑定─┤            │
│ └9.位移─┘│  │                     │ │ └─导出设置─┘            │
│          │  └─────────────────────┘ │                         │
│          │                          │    模板库                │
│ ┌被动技能┐│  ┌─────────────────────┐ │ ┌─收藏模板─┐            │
│ ├11.光环┤│  │   技能效果预览        │ │ ├─最近使用─┤            │
│ ├12.生存┤│  │                     │ │ ├─推荐模板─┤            │
│ ├13.采集┤│  │ [显示光环范围和效果] │ │ └─导入模板─┘            │
│ └14.战斗┘│  └─────────────────────┘ │                         │
├──────────┴──────────────────────────┴─────────────────────────┤
│ 状态栏: 当前步骤 | 技能类型 | 选中机制 | 兼容性状态            │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 技能创建流程界面
```typescript
interface SkillCreationUI {
  // === 向导式界面 ===
  wizardInterface: {
    // 步骤导航
    stepNavigation: {
      currentStep: number
      totalSteps: 8
      canSkipSteps: boolean
      canGoBack: boolean

      stepIndicator: {
        completed: boolean[]
        current: number
        errors: boolean[]
      }
    }

    // 机制选择界面
    mechanismSelection: {
      gridLayout: {
        columns: 5
        rows: 2
        mechanismCards: MechanismCard[]
      }

      mechanismCard: {
        icon: string
        name: string
        description: string
        complexity: 'simple' | 'medium' | 'complex'
        selected: boolean
        conflictsWith: string[]
      }

      selectionInfo: {
        selectedCount: number
        maxSelections: 5
        conflictWarnings: string[]
        complexityRating: number
      }
    }

    // 模板选择界面
    templateSelection: {
      // 分类浏览
      categoryBrowser: {
        categories: string[]
        currentCategory: string
        templatesPerPage: 12

        // 过滤和搜索
        filters: {
          difficulty: 'beginner' | 'intermediate' | 'advanced'
          gameType: 'mmorpg' | 'moba' | 'rts' | 'arpg'
          damageType: 'physical' | 'magical' | 'healing'
        }

        searchBar: {
          placeholder: '搜索技能模板...'
          suggestions: string[]
          recentSearches: string[]
        }
      }

      // 模板卡片
      templateCard: {
        thumbnail: string
        name: string
        description: string
        tags: string[]
        difficulty: number
        popularity: number

        // 预览功能
        preview: {
          quickPreview: boolean
          detailedPreview: boolean
          videoPreview: boolean
        }
      }
    }

    // 参数设置界面
    parameterSettings: {
      // 分组参数
      parameterGroups: {
        basic: {
          title: '基础参数'
          parameters: ['damage', 'range', 'cooldown', 'manaCost']
        }

        advanced: {
          title: '高级参数'
          parameters: ['castTime', 'channelTime', 'duration']
        }

        area: {
          title: '范围参数'
          parameters: ['shape', 'size', 'falloff']
        }
      }

      // 参数控件
      parameterControls: {
        slider: { min: number, max: number, step: number, value: number }
        dropdown: { options: string[], selected: string }
        checkbox: { checked: boolean, label: string }
        numberInput: { value: number, min: number, max: number }

        // 实时预览
        livePreview: {
          enabled: boolean
          updateDelay: number
          showChanges: boolean
        }
      }
    }
  }

  // === 测试界面 ===
  testingInterface: {
    // 测试环境
    testEnvironment: {
      // 测试场景
      testScenes: {
        emptyRoom: '空房间'
        targetDummies: '训练假人'
        multipleEnemies: '多个敌人'
        obstaclesCourse: '障碍场地'
        pvpArena: 'PVP竞技场'
      }

      // 测试目标
      testTargets: {
        dummy: { health: number, armor: number, resistances: {} }
        weakEnemy: { health: 100, armor: 0 }
        strongEnemy: { health: 1000, armor: 50 }
        boss: { health: 5000, armor: 100 }
      }
    }

    // 测试控制
    testControls: {
      playback: {
        play: boolean
        pause: boolean
        stop: boolean
        slowMotion: boolean
        frameByFrame: boolean
      }

      camera: {
        freeCamera: boolean
        followCaster: boolean
        followTarget: boolean
        topDown: boolean
      }

      debug: {
        showHitboxes: boolean
        showDamageNumbers: boolean
        showTimings: boolean
        showTrajectories: boolean
      }
    }

    // 测试结果
    testResults: {
      // 性能指标
      performance: {
        fps: number
        memoryUsage: number
        renderTime: number
        particleCount: number
      }

      // 平衡性分析
      balance: {
        dps: number
        dpm: number  // damage per mana
        efficiency: number
        riskReward: number
      }

      // 用户体验
      userExperience: {
        responsiveness: number
        clarity: number
        satisfaction: number
        learnability: number
      }
    }
  }
}
```

---

## 总结

技能编辑器作为机制分类的技能模板制作工具，提供了：

### 核心功能
- **13大机制分类**: 主动技能9类+被动技能4类，共260个预设模板
- **机制明确分类**: 按释放方式分类，用户一目了然技能机制
- **9步创建向导**: 从技能类型选择到测试完成的完整流程
- **智能组合系统**: 主动技能支持多机制组合，被动技能专注单一效果
- **系统开关集成**: 与游戏系统开关配合，支持不同游戏模式

### 用户体验
- **清晰的分类逻辑**: 主动技能vs被动技能，机制类型vs技能用途
- **渐进式复杂度**: 从简单模板到复杂组合
- **实时预览**: 即时查看技能效果，被动技能显示光环范围
- **智能推荐**: 基于选择的智能模板推荐和兼容性检查

### 技能类型特色
#### 主动技能 (9类×20个=180个模板)
- **需要主动释放**: 有冷却时间、法力消耗、施法时间
- **支持组合**: 可选择多个机制组合，按优先级执行
- **复杂效果**: 支持复杂的参数设置和特效绑定
- **专注战斗**: 专门用于战斗和角色增强，不包含生活技能

#### 被动技能 (4类×20个=80个模板)
- **自动生效**: 无需主动释放，始终激活或条件激活
- **单一机制**: 专注单一效果，不支持机制组合
- **持续影响**: 提供持续的属性加成、抗性、效率提升
- **生活技能精通**: 被动采集类包含生活技能精通（需要生活系统启用）

### 升级系统
- **多种升级方式**: 技能点、使用次数、资源、条件升级
- **灵活配置**: 支持自动升级和手动升级
- **质变升级**: 高等级解锁新机制和效果
- **被动技能升级**: 提升效果强度、扩大影响范围

### 系统集成
- **资源引用**: 无缝集成动画、特效、音频资源
- **标准化输出**: 为单位编辑器和事件触发器提供技能
- **兼容性保证**: 完整的兼容性检查系统
- **性能优化**: 内置性能分析和优化建议，特别关注被动技能的性能影响

### 适用游戏类型
- **纯战斗模式**: 专注主动战斗技能和被动战斗增益
- **生存建造模式**: 通过被动采集类技能增强生活系统
- **ARPG元素**: 完整的主动技能战斗系统
- **模块化设计**: 配合游戏系统开关支持不同游戏模式

### 与系统开关的集成
- **生活系统关闭**: 被动采集类中的生活技能精通不可用
- **生活系统开启**: 被动采集类技能可以增强建造、制作、采集效率
- **战斗系统**: 主动技能和被动战斗类技能始终可用
- **模块化加载**: 根据系统开关动态显示可用技能类型

**注意**:
- 复杂的技能逻辑将通过独立的**事件触发器编辑器**实现
- 建造和制作功能由独立的**生活系统编辑器**提供
- 技能编辑器专注于战斗和角色增强的模板化快速创建
