# 代码开发规则 (Code Development Rules)

## 1. 开发阶段控制
- ✅ **设计阶段**: 只进行架构设计、功能讨论、需求分析
- ✅ **规划阶段**: 制定详细的实现计划和模块划分
- ❌ **禁止**: 在设计未完成时写代码
- ❌ **禁止**: 在没有明确用户许可时开始编码

## 2. 代码结构原则
- **模块化**: 每个功能独立成模块，避免耦合
- **单一职责**: 每个文件/类只负责一个明确的功能
- **接口优先**: 先定义接口，再实现具体功能
- **渐进式**: 从核心功能开始，逐步扩展

## 3. 文件管理规则
- **命名规范**: 使用清晰的文件命名，反映功能用途
- **目录结构**: 按功能模块组织目录，不超过3层深度
- **依赖管理**: 明确记录模块间依赖关系
- **版本控制**: 重大修改前备份，支持回滚

## 4. 代码质量控制
- **删除优于添加**: 优先删除冗余代码，而非添加新功能
- **重构优于扩展**: 优先重构现有代码，而非创建新文件
- **简化优于复杂**: 选择最简单的实现方式
- **测试驱动**: 关键功能必须有对应测试

## 5. 冲突避免机制
- **功能边界**: 明确定义每个模块的功能边界
- **接口契约**: 模块间通过接口通信，不直接访问内部实现
- **配置分离**: 将配置与逻辑分离，避免硬编码
- **状态管理**: 集中管理应用状态，避免状态分散

## 6. 开发流程
```
1. 需求确认 → 2. 架构设计 → 3. 接口定义 → 4. 核心实现 → 5. 功能扩展 → 6. 测试验证 → 7. 优化重构
```

## 7. 代码审查检查点
- [ ] 是否有重复代码？
- [ ] 是否有未使用的代码？
- [ ] 是否有循环依赖？
- [ ] 是否有硬编码值？
- [ ] 是否有过度设计？
- [ ] 是否符合单一职责原则？

## 8. 禁止行为清单
- ❌ 复制粘贴代码
- ❌ 创建相似功能的多个实现
- ❌ 在一个文件中混合多种职责
- ❌ 使用全局变量
- ❌ 硬编码配置值
- ❌ 忽略错误处理
- ❌ 跳过代码审查

## 9. 重构触发条件
当出现以下情况时，必须重构：
- 代码重复度 > 20%
- 单个文件 > 300行
- 函数参数 > 5个
- 嵌套层级 > 3层
- 模块依赖 > 5个

## 10. 质量保证
- **代码覆盖率**: 核心功能 > 80%
- **性能基准**: 关键操作 < 100ms
- **内存使用**: 避免内存泄漏
- **错误处理**: 所有异常都要处理

## 11. 文档要求
- 每个模块必须有README
- 复杂算法必须有注释
- API必须有使用示例
- 重要决策必须记录原因

## 12. 协作规则
- 重大修改前必须讨论
- 删除代码前必须确认影响
- 新增依赖前必须评估必要性
- 性能优化前必须有基准测试

## 13. 代码完成后的强制要求

### 测试要求
- ✅ **功能测试**: 每个功能必须通过单元测试
- ✅ **集成测试**: 模块间交互必须测试
- ✅ **边界测试**: 测试极限情况和错误输入
- ✅ **性能测试**: 关键功能的性能基准测试
- ❌ **禁止**: 未经测试的代码提交

### 代码风格统一性
- **格式化**: 使用统一的代码格式化工具 (Prettier/ESLint)
- **命名规范**: 
  - 变量: camelCase (userName, itemCount)
  - 常量: UPPER_SNAKE_CASE (MAX_SIZE, DEFAULT_COLOR)
  - 类: PascalCase (VoxelEditor, BuildingGenerator)
  - 文件: kebab-case (voxel-editor.ts, building-generator.ts)
- **注释风格**: 使用JSDoc格式的统一注释
- **缩进**: 统一使用2个空格
- **引号**: 统一使用单引号

### 代码准确性验证
- **类型检查**: TypeScript严格模式，无any类型
- **语法检查**: 通过ESLint检查，无警告
- **逻辑验证**: 代码逻辑与设计文档一致
- **数据验证**: 输入输出数据格式正确
- **错误处理**: 所有可能的错误都有处理

### 成功运行验证
- **编译通过**: 代码必须无编译错误
- **启动成功**: 应用能够正常启动
- **功能验证**: 每个功能都能正常执行
- **无运行时错误**: 运行过程中无未捕获异常
- **内存稳定**: 无内存泄漏，内存使用稳定

## 14. 代码提交前检查清单
```
□ 代码编译通过
□ 所有测试通过
□ 代码格式化完成
□ 类型检查通过
□ 性能测试通过
□ 文档更新完成
□ 无TODO/FIXME标记
□ 无调试代码残留
□ 无硬编码值
□ 错误处理完整
```

## 15. 测试覆盖率要求
- **核心功能**: 100% 覆盖率
- **业务逻辑**: ≥ 90% 覆盖率
- **工具函数**: ≥ 85% 覆盖率
- **UI组件**: ≥ 70% 覆盖率

## 16. 性能基准
- **启动时间**: < 3秒
- **响应时间**: < 100ms (用户交互)
- **渲染帧率**: ≥ 60fps (3D渲染)
- **内存使用**: < 512MB (基础功能)
- **文件大小**: 单文件 < 50KB

## 17. 代码审查流程
```
1. 自我审查 → 2. 自动化测试 → 3. 代码格式检查 → 4. 功能验证 → 5. 性能测试 → 6. 文档检查 → 7. 最终确认
```

## 18. 质量门禁
如果以下任一条件不满足，**禁止**代码提交：
- ❌ 测试覆盖率不达标
- ❌ 代码格式不统一
- ❌ 存在TypeScript错误
- ❌ 存在ESLint警告
- ❌ 性能不达标
- ❌ 运行时错误
- ❌ 内存泄漏

## 19. 自动化工具配置
```json
{
  "scripts": {
    "test": "jest --coverage",
    "lint": "eslint src --ext .ts,.tsx",
    "format": "prettier --write src",
    "type-check": "tsc --noEmit",
    "build": "npm run lint && npm run type-check && npm run test && npm run build:prod",
    "validate": "npm run format && npm run lint && npm run type-check && npm run test"
  }
}
```

## 20. 持续集成要求
- **每次提交**: 自动运行测试和代码检查
- **每日构建**: 完整的集成测试
- **性能监控**: 持续监控性能指标
- **质量报告**: 定期生成代码质量报告

## 21. 错误处理标准
```typescript
// 统一的错误处理模式
try {
  // 业务逻辑
} catch (error) {
  logger.error('操作失败', { error, context });
  throw new CustomError('用户友好的错误信息', error);
}
```

## 22. 代码完成定义 (Definition of Done)
代码只有满足以下所有条件才算完成：
- ✅ 功能实现完整
- ✅ 测试全部通过
- ✅ 代码风格统一
- ✅ 性能达标
- ✅ 文档完整
- ✅ 无已知缺陷
- ✅ 代码审查通过
- ✅ 成功运行验证

---

**核心原则**: 先思考，再行动；先删除，再添加；先简化，再复杂化；先测试，再提交。
