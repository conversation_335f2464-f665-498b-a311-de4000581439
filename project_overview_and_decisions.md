# 体素游戏编辑器系统 - 项目总览与决策记录

## 1. 项目概述

### 1.1 项目目标
开发一个模块化的体素风格游戏编辑器系统，支持MC+七日杀类型的游戏开发，包含生存、ARPG、探索和建造元素。

### 1.2 核心设计理念
- **模块化架构**: 将复杂的单一编辑器拆分为专业化的独立模块
- **系统开关控制**: 通过配置支持不同游戏模式和体验
- **机制明确分类**: 按功能机制而非用途分类，用户一目了然
- **专业化分工**: 每个编辑器专注特定领域，提高专业性

## 2. 架构演进历程

### 2.1 初始设计
- 单一复杂的单位数据编辑器
- 功能混杂，难以维护和使用

### 2.2 第一次重构 - 模块化拆分
- 拆分为6个独立编辑器
- 建立资源引用和集成系统
- 每个模块专注特定功能

### 2.3 第二次重构 - 技能系统优化
**关键决策**: 区分技能机制类型 vs 技能用途分类
- **问题**: 混淆了技能的释放机制和技能的用途效果
- **解决方案**: 按释放方式分类（主动技能 vs 被动技能）
- **结果**: 用户能清晰理解每种技能的工作方式

### 2.4 第三次重构 - 生活系统独立
**关键决策**: 建造制作不应该是技能
- **问题**: 制造类技能实际上是基础游戏系统，不是角色技能
- **解决方案**: 
  - 删除制造类主动技能
  - 创建独立的生活系统编辑器
  - 在被动技能中保留生活技能精通
- **结果**: 技能系统专注战斗，生活功能独立且可选

### 2.5 第四次重构 - 系统开关集成
**关键决策**: 需要模块化的游戏体验控制
- **问题**: 不同玩家想要不同的游戏体验（纯战斗 vs 生存建造）
- **解决方案**: 创建游戏系统开关
- **结果**: 同一个游戏支持多种模式，模块化加载

## 3. 当前架构状态

### 3.1 核心编辑器模块 (6个)
1. **体素模型编辑器** - 基础3D模型创建
2. **动画编辑器** - 动作和动画序列
3. **特效编辑器** - 视觉特效和粒子系统
4. **音频编辑器** - 音乐音效和3D音频
5. **技能编辑器** - 战斗技能和角色增强 (专注战斗)
6. **单位编辑器** - 资源集成和单位数据管理

### 3.2 可选模块 (2个)
7. **生活系统编辑器** - 建造、制作、生存系统 (可选启用)
8. **物品编辑器** - 装备道具和基于技能的属性系统

### 3.3 系统控制
9. **游戏系统开关** - 模块化游戏体验控制

### 3.4 技能系统详情
**主动技能** (9类 × 20个 = 180个模板):
1. 单体矢类 - 发射投射物，可被阻挡
2. 单体锁定类 - 瞬间锁定目标，无视障碍
3. 弹射链类 - 多目标连锁传递
4. 范围瞬发类 - 指定地点瞬间生效
5. 范围持续类 - 区域内持续作用
6. 自身增益类 - 对施法者自身产生效果
7. 放置类 - 地面放置战斗物体或陷阱
8. 召唤类 - 召唤单位或生物
9. 位移类 - 改变位置

**被动技能** (4类 × 20个 = 80个模板):
1. 被动光环类 - 持续影响周围单位
2. 被动生存类 - 环境适应和生存能力
3. 被动采集类 - 采集效率和生活技能精通
4. 被动战斗类 - 战斗属性和能力增强

**装备/道具技能** (6类 × 20个 = 120个模板):
1. 装备被动技能 - 装备时自动激活的属性加成
2. 装备主动技能 - 装备后可手动释放的技能
3. 装备触发技能 - 条件触发的装备技能
4. 道具使用技能 - 使用道具时释放的技能
5. 道具被动技能 - 持有道具时生效的技能
6. 套装技能 - 套装件数激活的技能

**总计**: 19类技能机制，380个预设模板

## 4. 游戏系统开关

### 4.1 核心系统模块
- **生活系统**: 建造、制作、生存需求、资源采集
- **战斗系统**: 技能、装备、等级、属性
- **探索系统**: 地牢、任务、宝藏、地图
- **社交系统**: 多人游戏、公会、交易、聊天
- **经济系统**: 货币、市场、拍卖、银行

### 4.2 游戏模式预设
- **纯战斗模式**: 专注战斗和探索的ARPG体验
- **生存建造模式**: 完整的生存建造体验
- **MMO模式**: 大型多人在线游戏体验
- **创造模式**: 专注建造和创作的模式

## 5. 关键设计决策记录

### 5.1 技能分类决策
**时间**: 对话中期
**问题**: 用户指出混淆了技能机制和技能效果
**决策**: 按释放方式分类，而非用途分类
**影响**: 用户界面更清晰，技能机制一目了然

### 5.2 制造系统独立决策
**时间**: 对话后期
**问题**: 用户指出建造制作应该是基础系统，不是技能
**决策**: 删除制造类技能，创建独立生活系统编辑器
**影响**: 技能系统更专业，生活系统更完整

### 5.3 系统开关决策
**时间**: 对话最后
**问题**: 需要支持不同游戏模式
**决策**: 创建独立的系统开关配置
**影响**: 同一游戏支持多种体验，模块化加载

## 6. 文档状态

### 6.1 已完成文档
- ✅ `modular_architecture_overview.md` - 整体架构概述
- ✅ `skill_editor_specification.md` - 技能编辑器详细规格
- ✅ `game_system_switches_specification.md` - 系统开关规格
- ✅ `voxel_model_editor_specification.md` - 体素模型编辑器
- ✅ `animation_editor_specification.md` - 动画编辑器
- ✅ `effect_editor_specification.md` - 特效编辑器
- ✅ `audio_editor_specification.md` - 音频编辑器
- ✅ `unit_editor_specification.md` - 单位编辑器

### 6.2 需要创建的文档
- ⏳ `life_system_editor_specification.md` - 生活系统编辑器规格
- ⏳ `integration_guide.md` - 模块间集成指南
- ⏳ `development_roadmap.md` - 开发路线图

## 7. 下一步计划

### 7.1 立即需要
1. 完善生活系统编辑器的详细规格
2. 定义模块间的集成接口
3. 制定开发优先级和时间线

### 7.2 中期目标
1. 实现核心编辑器的基础功能
2. 建立资源引用和兼容性检查系统
3. 开发系统开关的配置界面

### 7.3 长期目标
1. 完整的编辑器套件
2. 社区分享和模板库
3. 插件和扩展系统

## 8. 技术特色

### 8.1 创新点
- **机制分类**: 按技能释放机制分类，而非传统的职业分类
- **模块化控制**: 通过系统开关实现游戏体验的模块化配置
- **专业分工**: 每个编辑器专注特定领域，提高专业性
- **灵活集成**: 标准化的资源引用系统，支持无缝集成

### 8.2 适用场景
- MC风格的体素建造游戏
- 七日杀类型的生存游戏
- ARPG风格的战斗游戏
- 多人在线游戏
- 创造和分享平台

---

## 9. 物品编辑器设计决策

### 9.1 核心设计理念
- **基于技能的属性系统**: 装备不直接提供属性加成，而是通过绑定技能实现所有效果
- **完整的物品分类**: 装备类（可穿戴）和道具类（物品栏/背包）的清晰分工
- **模块化系统开关**: 容器、绑定、耐久度、品质等系统都可独立开关
- **脚本库分类**: ##物品脚本##标识符确保脚本的专用性和安全性

### 9.2 与其他编辑器的集成
- **技能编辑器**: 扩展支持6类装备/道具专用技能（120个模板）
- **体素编辑器**: 深度集成物品3D模型的自动/手动生成系统
- **生活系统编辑器**: 制作材料、建造物品、种子等的关联支持
- **特效/音频编辑器**: 物品使用特效和音效的完整绑定

### 9.3 物品获取和交互
- **多样获取方式**: 制作（合成/分解/种植）、掉落、商店、任务、宝箱
- **联机交互**: 物品丢到地上可被任意玩家拾取，无复杂交易系统
- **绑定机制**: 支持拾取绑定、装备绑定、使用绑定、任务绑定
- **容器嵌套**: 支持背包套娃等复杂容器系统

### 9.4 技术特色
- **参考WE设计**: 借鉴魔兽争霸3物品编辑器的成熟设计理念
- **6步创建向导**: 从物品类型选择到预览测试的完整流程
- **智能验证**: 模型尺寸、复杂度、性能的自动检查和优化建议
- **品质系统**: 完整的品质等级和颜色编码系统

---

## 10. 给新对话窗口AI的说明

如果你是在新的对话窗口中接手这个项目，请注意：

1. **项目已经过多轮深度讨论和重构**
2. **当前架构是经过仔细考虑的最终方案**
3. **关键决策都有明确的原因和背景**
4. **所有主要文档都已完成并保持同步**

请基于这个文档的内容继续讨论，避免重复之前已经解决的问题。如果需要了解某个决策的详细背景，可以参考第5章的决策记录。

---

## 11. 详细对话记录摘要

### 11.1 项目启动阶段
- **用户需求**: 开发MC体素风格+七日杀类型的游戏，包含生存、ARPG、探索、建造
- **初始方案**: 模块化编辑器架构，6个独立编辑器
- **关键讨论**: 如何平衡复杂性和专业性

### 11.2 技能系统深度讨论
- **用户观点**: 技能应该按机制分类，不是按用途分类
- **具体例子**:
  - ❌ 错误: "光环类技能" - 这是效果，不是机制
  - ✅ 正确: "被动光环类" - 这是机制类型
- **重要区分**:
  - 技能机制 = 如何释放 (主动 vs 被动)
  - 技能效果 = 做什么 (伤害、治疗、控制等)

### 11.3 制造系统争议
- **用户质疑**: "建筑制造应该不算是技能，应该算是一个系统？"
- **用户观点**: "既然是有建造类型的游戏应该算是基础系统吧？"
- **AI认同**: 完全正确，建造制作是基础游戏系统，不应该作为"技能"
- **解决方案**: 创建独立的生活系统编辑器

### 11.4 系统开关的提出
- **用户建议**: "做一个独立的系统开关，来实行一些功能的开关"
- **具体需求**: 生活系统可选，关闭后游戏变成纯战斗模式
- **扩展思考**: 支持不同游戏模式的模块化配置

### 11.5 技能精通的处理
- **用户建议**: "在被动技能中预设几个制造技能的精通，比如炼金术精通、附魔术精通等"
- **设计思路**: 技能可以增强生活系统，但生活系统本身是独立的
- **实现方式**: 被动采集类技能包含生活技能精通

### 11.6 物品编辑器设计
- **核心理念**: 装备不直接提供属性，而是通过绑定技能实现效果
- **技能扩展**: 新增6类装备/道具专用技能（120个模板）
- **系统集成**: 与体素编辑器深度集成，支持物品3D模型生成
- **脚本库**: ##物品脚本##标识符确保脚本专用性

### 11.7 最终架构确认
- **主动技能**: 9类，专注战斗和角色能力
- **被动技能**: 4类，包含生活技能精通
- **装备/道具技能**: 6类，专用于物品系统
- **生活系统**: 独立编辑器，可选启用
- **物品系统**: 独立编辑器，基于技能的属性系统
- **系统开关**: 模块化控制游戏体验
- **总模板数**: 380个（19类 × 20个）

### 11.8 用户反馈模式
- **用户特点**: 非常注重概念的准确性和逻辑的清晰性
- **纠错方式**: 直接指出概念混淆，提供正确的理解方式
- **设计偏好**: 模块化、专业化、职责明确
- **沟通风格**: 简洁直接，重视实用性

### 11.9 关键转折点
1. **技能机制 vs 效果的区分** - 让整个技能系统更清晰
2. **制造系统的独立** - 让职责分工更明确
3. **系统开关的引入** - 让游戏体验更灵活

## 12. 新窗口AI接手指南

### 12.1 用户特点
- 有清晰的系统设计思维
- 重视概念的准确性
- 偏好模块化和专业化设计
- 会直接指出设计中的问题

### 12.2 沟通建议
- 避免混淆机制和效果
- 保持职责分工的清晰性
- 重视用户的纠正和建议
- 基于已有决策继续讨论

### 12.3 当前状态
- 架构设计已基本完成
- 主要文档已同步更新
- 系统开关已集成到整体架构
- 生活系统编辑器规格已完成
- 物品编辑器规格已完成，技能系统已扩展支持装备/道具技能
- 可以开始讨论具体实现细节

### 12.4 可能的后续话题
- 体素编辑器的物品生成系统设计
- 模块间集成接口的定义
- 开发优先级和时间规划
- 具体技术实现方案
- 事件触发器编辑器的设计
