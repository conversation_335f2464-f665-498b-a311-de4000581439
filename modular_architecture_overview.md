# 模块化编辑器架构总览

## 1. 架构概述

本系统采用完全模块化的设计，将原本复杂的单位数据编辑器拆分为6个独立但相互协作的专业编辑器，并配备游戏系统开关进行模块化控制。每个编辑器专注于特定领域，通过标准化的资源引用系统实现无缝集成，支持不同游戏模式的灵活配置。

### 1.1 模块关系图
```
┌─────────────────────────────────────────────────────────────┐
│                    模块化编辑器系统                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ 体素模型     │───→│ 动画编辑器   │───→│ 单位编辑器   │     │
│  │ 编辑器      │    │            │    │ (集成中心)  │     │
│  │ (基础资源)  │    │ (动作动画)  │    │            │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         │                   ↓                   │          │
│         │            ┌─────────────┐            │          │
│         │            │ 特效编辑器   │            │          │
│         │            │ (视觉特效)  │            │          │
│         │            └─────────────┘            │          │
│         │                   │                   │          │
│         │                   ↓                   │          │
│         │            ┌─────────────┐            │          │
│         │            │ 音频编辑器   │            │          │
│         │            │ (音乐音效)  │            │          │
│         │            └─────────────┘            │          │
│         │                   │                   │          │
│         └───────────────────┼───────────────────┘          │
│                             ↓                              │
│                     ┌─────────────┐                        │
│                     │ 技能编辑器   │                        │
│                     │ (战斗逻辑)  │                        │
│                     └─────────────┘                        │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   系统控制层                             │ │
│ │                                                         │ │
│ │  ┌─────────────┐              ┌─────────────┐          │ │
│ │  │ 游戏系统     │              │ 生活系统     │          │ │
│ │  │ 开关        │◄────────────►│ 编辑器      │          │ │
│ │  │ (模块控制)  │              │ (可选模块)  │          │ │
│ │  └─────────────┘              └─────────────┘          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 数据流向
```
体素模型(.voxel) → 动画编辑器 → 动画文件(.anim)
                                      ↓
特效编辑器 → 特效文件(.effect) ────────┐
                                      ↓
音频编辑器 → 音频文件(.audio) → 技能编辑器 → 技能文件(.skill)
                                      ↓
                              单位编辑器 → 单位文件(.unit)
```

## 2. 游戏系统开关

### 2.1 系统开关概述
游戏系统开关是一个独立的配置系统，允许开发者和玩家选择性地启用或禁用特定的游戏系统模块。通过这种方式，同一个游戏可以支持不同的游戏模式和体验。

### 2.2 核心系统模块
- **生活系统**: 建造、制作、生存需求、资源采集
- **战斗系统**: 技能、装备、等级、属性
- **探索系统**: 地牢、任务、宝藏、地图
- **社交系统**: 多人游戏、公会、交易、聊天
- **经济系统**: 货币、市场、拍卖、银行

### 2.3 游戏模式预设
- **纯战斗模式**: 专注战斗和探索的ARPG体验
- **生存建造模式**: 完整的生存建造体验
- **MMO模式**: 大型多人在线游戏体验
- **创造模式**: 专注建造和创作的模式

### 2.4 与编辑器的集成
- **技能编辑器**: 根据系统开关显示/隐藏相关技能类型
- **单位编辑器**: 根据系统开关显示/隐藏相关属性
- **生活系统编辑器**: 仅在生活系统启用时可用

## 3. 各模块详细说明

### 3.1 体素模型编辑器
**职责**: 创建和编辑3D体素模型
**输出**: .voxel模型文件 + 元数据

#### 核心功能
- **专业体素编辑工具**: 从基础到高级的完整工具集
- **丰富材质系统**: 有机、无机、魔法、特殊材质
- **智能部位标记**: 自动检测+手动标记身体部位
- **兼容性元数据**: 为其他模块提供详细兼容性信息

#### 关键特性
- 支持对称编辑和批量操作
- 内置噪声工具和程序化生成
- 完整的材质预设库
- 自动LOD生成和性能优化

### 2.2 动画编辑器
**职责**: 创建基于体素的动画脚本
**输入**: .voxel模型文件
**输出**: .anim动画文件 + 标记数据

#### 核心功能
- **专业动画脚本系统**: 支持复杂的动画逻辑
- **智能部位绑定**: 自动识别模型部位并绑定
- **丰富动画模板**: 移动、战斗、情绪等各类动画
- **时间轴编辑器**: 专业级关键帧编辑工具

#### 关键特性
- 支持多轨道并行动画
- 内置物理约束系统
- 参数化动画控制
- 实时预览和调试

### 2.3 特效编辑器
**职责**: 创建各种视觉特效
**输出**: .effect特效文件 + 组合数据

#### 核心功能
- **完整粒子系统**: 多种发射器和行为控制
- **专业光效系统**: 各种光源和特殊光效
- **强大后处理**: 屏幕空间和体积效果
- **模板化创建**: 魔法、战斗、环境特效模板

#### 关键特性
- 支持特效组合和层级管理
- 内置性能优化和LOD
- 实时预览和参数调节
- 与动画系统的时间同步

### 2.4 技能编辑器
**职责**: 创建战斗技能和角色增强系统
**输入**: .anim动画文件 + .effect特效文件
**输出**: .skill技能文件

#### 核心功能
- **13大机制分类**: 主动技能9类+被动技能4类，共260个预设模板
- **机制明确分类**: 按释放方式分类，用户一目了然技能机制
- **组合式创建系统**: 主动技能支持多机制组合
- **系统开关集成**: 与游戏系统开关配合，支持不同游戏模式

#### 关键特性
- 专注战斗和角色增强，不包含生活技能
- 被动采集类技能包含生活技能精通（需要生活系统启用）
- 9步创建向导，从技能类型选择到测试完成
- 内置调试和测试工具，支持平衡性分析
- 智能兼容性检查

### 2.5 音频编辑器
**职责**: 创建游戏音乐和音效
**输出**: .audio音频文件 + MIDI数据

#### 核心功能
- **多模式编辑**: 简化模式、钢琴卷帘、五线谱三种编辑模式
- **智能音乐生成**: 基于风格和情绪的自动音乐生成
- **完整音效库**: 环境、战斗、UI等各类音效模板
- **MML文本支持**: 完整的MML导入和编辑功能

#### 关键特性
- 支持多种音乐制作工作流
- 内置MIDI生成引擎
- 丰富的预设模板库
- 与游戏系统的动态集成

### 2.6 单位编辑器 (集成中心)
**职责**: 组装完整的游戏单位
**输入**: 所有其他模块的输出文件
**输出**: .unit单位配置文件

#### 核心功能
- **完整资源引用系统**: 集成所有其他编辑器资源
- **6步创建向导**: 降低使用门槛的向导式创建
- **智能推荐系统**: 基于兼容性的智能推荐
- **实时预览测试**: 完整的单位预览和测试

#### 关键特性
- 引用式设计避免资源重复
- 多层次兼容性检查
- 完整的属性配置系统
- 内置平衡性分析

## 3. 模块间协作机制

### 3.1 资源引用系统
```typescript
interface ResourceReference {
  // 资源标识
  resourceId: string
  resourceType: 'model' | 'animation' | 'effect' | 'skill'
  resourcePath: string
  
  // 版本控制
  version: string
  lastModified: Date
  checksum: string
  
  // 依赖关系
  dependencies: ResourceReference[]
  dependents: ResourceReference[]
  
  // 兼容性信息
  compatibility: {
    requiredFeatures: string[]
    supportedVersions: string[]
    performanceImpact: PerformanceMetrics
  }
}
```

### 3.2 标记和元数据系统
每个模块都会为其输出资源生成详细的标记和元数据：

#### 模型标记
- 身体结构信息 (人形、四足、飞行等)
- 部位标记和层级关系
- 材质类型和渲染需求
- 性能和复杂度信息

#### 动画标记
- 功能标记 (移动、攻击、待机等)
- 身体结构需求
- 循环性和可混合性
- 性能需求和优化级别

#### 特效标记
- 特效类型和用途
- 性能影响和资源需求
- 适用场景和环境
- 与其他特效的兼容性

#### 技能标记
- 技能类型和分类
- 资源需求 (动画、特效)
- 平衡性参数
- AI使用建议

### 3.3 兼容性检查机制
```typescript
interface CompatibilityChecker {
  // 检查模型与动画兼容性
  checkModelAnimationCompatibility(
    modelId: string, 
    animationId: string
  ): CompatibilityResult
  
  // 检查动画与特效兼容性
  checkAnimationEffectCompatibility(
    animationId: string, 
    effectId: string
  ): CompatibilityResult
  
  // 检查技能资源兼容性
  checkSkillResourceCompatibility(
    skillId: string, 
    resources: ResourceReference[]
  ): CompatibilityResult
  
  // 检查整体单位兼容性
  checkUnitCompatibility(
    unitConfiguration: UnitConfiguration
  ): CompatibilityResult
}
```

## 4. 用户工作流程

### 4.1 典型创建流程
```
1. 体素模型编辑器 → 创建生物模型 → 标记身体部位
2. 动画编辑器 → 导入模型 → 创建动画 → 测试效果
3. 特效编辑器 → 创建视觉特效 → 调整参数
4. 音频编辑器 → 创建音乐音效 → 配置触发条件
5. 技能编辑器 → 组合动画、特效、音效 → 编写技能逻辑
6. 单位编辑器 → 集成所有资源 → 配置属性 → 最终测试
```

### 4.2 协作工作流程
```
美术人员: 体素模型编辑器 + 特效编辑器
动画师: 动画编辑器
音频师: 音频编辑器
策划人员: 技能编辑器 + 单位编辑器
程序人员: 各模块的技术支持和扩展
```

## 5. 技术优势

### 5.1 开发优势
- **并行开发**: 各模块可独立开发和测试
- **专业化**: 每个模块专注特定领域，功能更专业
- **可维护性**: 模块化设计便于维护和更新
- **可扩展性**: 易于添加新功能和模块

### 5.2 用户优势
- **学习曲线**: 用户可逐步学习各个模块
- **专业工具**: 每个工具都针对特定任务优化
- **资源复用**: 避免重复工作，提高效率
- **团队协作**: 支持多人协作开发

### 5.3 系统优势
- **资源管理**: 统一的资源管理和版本控制
- **性能优化**: 各模块独立优化，整体性能更好
- **质量保证**: 多层次的兼容性和质量检查
- **标准化**: 统一的接口和数据格式

## 6. 实施建议

### 6.1 开发顺序
1. **第一阶段**: 体素模型编辑器 (基础)
2. **第二阶段**: 动画编辑器 (依赖模型)
3. **第三阶段**: 特效编辑器 (独立开发)
4. **第四阶段**: 音频编辑器 (独立开发)
5. **第五阶段**: 技能编辑器 (集成动画、特效、音频)
6. **第六阶段**: 单位编辑器 (最终集成)

### 6.2 测试策略
- **单元测试**: 每个模块独立测试
- **集成测试**: 模块间协作测试
- **用户测试**: 完整工作流程测试
- **性能测试**: 整体系统性能测试

### 6.3 用户培训
- **分模块培训**: 针对不同用户角色的专门培训
- **工作流程培训**: 完整的协作流程培训
- **最佳实践**: 提供最佳实践指南和案例

---

## 总结

这个模块化架构设计实现了：

### 专业化分工
每个编辑器都专注于特定领域，提供专业级的功能和用户体验

### 无缝集成
通过标准化的资源引用和兼容性检查，确保各模块间的完美协作

### 用户友好
从新手友好的向导式创建到专业用户的高级功能，满足不同层次的需求

### 可持续发展
模块化设计为未来的功能扩展和技术升级提供了良好的基础

### 2.7 生活系统编辑器 (可选模块)
**职责**: 创建建造、制作、生存系统
**输出**: .life生活系统文件

#### 核心功能
- **建造系统**: 方块放置规则、建筑蓝图、多方块结构
- **制作系统**: 合成配方、制作台、加工流程、品质系统
- **生存系统**: 饥饿口渴、温度系统、健康状态、疲劳管理
- **资源系统**: 采集规则、资源分布、工具需求

#### 关键特性
- 仅在生活系统启用时可用
- 与技能编辑器的被动采集类技能联动
- 支持MC风格的体素建造
- 完整的生存需求管理

#### 系统开关集成
- **生活系统关闭**: 编辑器不可用，游戏专注战斗
- **生活系统开启**: 编辑器可用，提供完整生活体验
- **创造模式**: 建造和制作功能可用，生存需求关闭

---

## 4. 总结

这个架构不仅解决了原有单一编辑器的复杂性问题，还通过游戏系统开关实现了模块化的游戏体验控制，为团队协作和专业化开发提供了完美的解决方案！

### 核心优势
- **模块化设计**: 每个编辑器专注特定领域
- **灵活配置**: 通过系统开关支持不同游戏模式
- **专业工具**: 每个模块都有专门的编辑器
- **无缝集成**: 标准化的资源引用系统
