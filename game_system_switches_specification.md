# 游戏系统开关规格文档

## 1. 概述

游戏系统开关是一个独立的配置系统，允许开发者和玩家选择性地启用或禁用特定的游戏系统模块。通过这种方式，同一个游戏可以支持不同的游戏模式和体验，从纯战斗ARPG到完整的生存建造游戏。

## 2. 核心功能

### 2.1 系统开关架构
```typescript
interface GameSystemSwitches {
  // === 核心系统开关 ===
  coreSystemSwitches: {
    // 生活系统开关
    lifeSystem: {
      enabled: boolean
      description: '启用完整的生存建造系统'
      includes: [
        'building_system',      // 建造系统
        'crafting_system',      // 制作系统
        'survival_needs',       // 生存需求
        'resource_gathering',   // 资源采集
        'farming_system',       // 农业系统
        'cooking_system',       // 烹饪系统
        'weather_system',       // 天气系统
        'day_night_cycle'       // 昼夜循环
      ]
      
      // 子系统开关
      subSystems: {
        buildingSystem: {
          enabled: boolean
          features: {
            blockPlacement: boolean      // 方块放置
            structureBlueprints: boolean // 建筑蓝图
            multiBlockStructures: boolean // 多方块结构
            decorativeBlocks: boolean    // 装饰方块
          }
        }
        
        craftingSystem: {
          enabled: boolean
          features: {
            basicCrafting: boolean       // 基础制作
            advancedCrafting: boolean    // 高级制作
            qualitySystem: boolean       // 品质系统
            enchantingSystem: boolean    // 附魔系统
            repairSystem: boolean        // 修理系统
          }
        }
        
        survivalNeeds: {
          enabled: boolean
          features: {
            hungerSystem: boolean        // 饥饿系统
            thirstSystem: boolean        // 口渴系统
            temperatureSystem: boolean   // 温度系统
            fatigueSystem: boolean       // 疲劳系统
            healthSystem: boolean        // 健康系统
          }
        }
        
        resourceGathering: {
          enabled: boolean
          features: {
            miningSystem: boolean        // 挖掘系统
            loggingSystem: boolean       // 伐木系统
            farmingSystem: boolean       // 农业系统
            fishingSystem: boolean       // 钓鱼系统
            huntingSystem: boolean       // 狩猎系统
          }
        }
      }
    }
    
    // 战斗系统开关
    combatSystem: {
      enabled: boolean
      description: '启用完整的战斗系统'
      includes: [
        'skill_system',         // 技能系统
        'equipment_system',     // 装备系统
        'level_system',         // 等级系统
        'attribute_system',     // 属性系统
        'buff_debuff_system',   // 增益减益系统
        'damage_system',        // 伤害系统
        'ai_combat_system'      // AI战斗系统
      ]
      
      subSystems: {
        skillSystem: {
          enabled: boolean
          features: {
            activeSkills: boolean        // 主动技能
            passiveSkills: boolean       // 被动技能
            skillTrees: boolean          // 技能树
            skillUpgrade: boolean        // 技能升级
          }
        }
        
        equipmentSystem: {
          enabled: boolean
          features: {
            weaponSystem: boolean        // 武器系统
            armorSystem: boolean         // 护甲系统
            accessorySystem: boolean     // 饰品系统
            durabilitySystem: boolean    // 耐久系统
          }
        }
      }
    }
    
    // 探索系统开关
    explorationSystem: {
      enabled: boolean
      description: '启用探索和冒险系统'
      includes: [
        'dungeon_system',       // 地牢系统
        'quest_system',         // 任务系统
        'treasure_system',      // 宝藏系统
        'map_system',           // 地图系统
        'transportation_system' // 交通系统
      ]
    }
    
    // 社交系统开关
    socialSystem: {
      enabled: boolean
      description: '启用多人社交系统'
      includes: [
        'multiplayer_system',   // 多人游戏
        'guild_system',         // 公会系统
        'trade_system',         // 交易系统
        'chat_system',          // 聊天系统
        'friend_system'         // 好友系统
      ]
    }
    
    // 经济系统开关
    economySystem: {
      enabled: boolean
      description: '启用经济和贸易系统'
      includes: [
        'currency_system',      // 货币系统
        'market_system',        // 市场系统
        'auction_system',       // 拍卖系统
        'bank_system',          // 银行系统
        'tax_system'            // 税收系统
      ]
    }
  }
  
  // === 游戏模式预设 ===
  gameModePresets: {
    // 纯战斗模式
    pureCombat: {
      name: '纯战斗模式'
      description: '专注战斗和探索的ARPG体验'
      systemSettings: {
        lifeSystem: false
        combatSystem: true
        explorationSystem: true
        socialSystem: false
        economySystem: false
      }
      features: [
        '专注战斗技能和装备',
        '通过战斗获得经验和装备',
        '探索地牢和完成任务',
        '无生存需求压力'
      ]
    }
    
    // 生存建造模式
    survivalBuilding: {
      name: '生存建造模式'
      description: '完整的生存建造体验'
      systemSettings: {
        lifeSystem: true
        combatSystem: true
        explorationSystem: true
        socialSystem: false
        economySystem: false
      }
      features: [
        '完整的建造和制作系统',
        '生存需求管理',
        '资源采集和加工',
        '环境适应和基地建设'
      ]
    }
    
    // MMO模式
    mmoMode: {
      name: 'MMO模式'
      description: '大型多人在线游戏体验'
      systemSettings: {
        lifeSystem: true
        combatSystem: true
        explorationSystem: true
        socialSystem: true
        economySystem: true
      }
      features: [
        '所有系统全部启用',
        '完整的社交和经济系统',
        '公会和团队合作',
        '复杂的游戏生态'
      ]
    }
    
    // 创造模式
    creativeMode: {
      name: '创造模式'
      description: '专注建造和创作的模式'
      systemSettings: {
        lifeSystem: {
          buildingSystem: true,
          craftingSystem: true,
          survivalNeeds: false,
          resourceGathering: false
        }
        combatSystem: false
        explorationSystem: false
        socialSystem: true
        economySystem: false
      }
      features: [
        '无限资源建造',
        '无生存压力',
        '专注创作和分享',
        '社交展示功能'
      ]
    }
  }
  
  // === 系统依赖关系 ===
  systemDependencies: {
    // 依赖关系定义
    dependencies: {
      craftingSystem: {
        requires: ['resourceGathering']
        description: '制作系统需要资源采集系统'
      }
      
      enchantingSystem: {
        requires: ['craftingSystem', 'combatSystem']
        description: '附魔系统需要制作系统和战斗系统'
      }
      
      guildSystem: {
        requires: ['socialSystem', 'combatSystem']
        description: '公会系统需要社交系统和战斗系统'
      }
      
      marketSystem: {
        requires: ['economySystem', 'socialSystem']
        description: '市场系统需要经济系统和社交系统'
      }
    }
    
    // 冲突关系定义
    conflicts: {
      creativeMode: {
        conflicts: ['survivalNeeds', 'resourceGathering']
        description: '创造模式与生存需求冲突'
      }
    }
  }
}
```

## 3. 用户界面设计

### 3.1 系统开关界面
```
┌─────────────────────────────────────────────────────────────┐
│ 游戏系统配置                                                │
├─────────────────────────────────────────────────────────────┤
│ 快速预设: [纯战斗] [生存建造] [MMO模式] [创造模式] [自定义] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─核心系统─────────────────┐ ┌─系统详情─────────────────┐   │
│ │ ☑ 生活系统               │ │ 生活系统包含:             │   │
│ │   ├─☑ 建造系统           │ │ • 建造系统 - 方块放置     │   │
│ │   ├─☑ 制作系统           │ │ • 制作系统 - 物品合成     │   │
│ │   ├─☐ 生存需求           │ │ • 生存需求 - 饥饿口渴     │   │
│ │   └─☑ 资源采集           │ │ • 资源采集 - 挖掘伐木     │   │
│ │                         │ │                           │   │
│ │ ☑ 战斗系统               │ │ 影响的编辑器:             │   │
│ │   ├─☑ 技能系统           │ │ • 技能编辑器 - 被动技能   │   │
│ │   ├─☑ 装备系统           │ │ • 单位编辑器 - 生活属性   │   │
│ │   └─☑ 等级系统           │ │ • 需要生活系统编辑器     │   │
│ │                         │ │                           │   │
│ │ ☑ 探索系统               │ │ 系统依赖检查:             │   │
│ │ ☐ 社交系统               │ │ ✓ 所有依赖满足           │   │
│ │ ☐ 经济系统               │ │ ⚠ 建议启用生存需求       │   │
│ └─────────────────────────┘ └─────────────────────────┘   │
│                                                             │
│ [应用配置] [重置] [导出配置] [导入配置]                     │
└─────────────────────────────────────────────────────────────┘
```

## 4. 系统集成

### 4.1 与编辑器的集成
- **技能编辑器**: 根据系统开关显示/隐藏相关技能类型
- **单位编辑器**: 根据系统开关显示/隐藏相关属性
- **生活系统编辑器**: 仅在生活系统启用时可用
- **场景编辑器**: 根据系统开关调整可用功能

### 4.2 运行时系统管理
- **动态加载**: 只加载启用的系统模块
- **性能优化**: 禁用的系统不占用资源
- **兼容性检查**: 自动检查系统依赖关系
- **配置保存**: 支持配置文件的导入导出

---

## 总结

游戏系统开关提供了：

### 核心功能
- **模块化控制**: 精确控制每个游戏系统的启用状态
- **预设模式**: 提供常见游戏模式的快速配置
- **依赖管理**: 自动处理系统间的依赖关系
- **冲突检测**: 防止不兼容的系统组合

### 用户体验
- **灵活配置**: 支持从纯战斗到完整MMO的各种模式
- **简单操作**: 预设模式一键配置
- **可视化界面**: 清晰显示系统状态和依赖关系
- **配置管理**: 支持配置的保存和分享

### 开发优势
- **模块化开发**: 每个系统可以独立开发和测试
- **性能优化**: 只加载需要的系统模块
- **维护简化**: 系统间的耦合度降低
- **扩展性强**: 新系统可以轻松集成到开关系统中
