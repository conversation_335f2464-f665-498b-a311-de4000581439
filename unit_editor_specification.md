# 单位编辑器规格文档

## 1. 概述

单位编辑器是整个模块化系统的集成中心，专门用于组装和配置游戏单位。通过引用体素模型编辑器的模型、动画编辑器的动画、特效编辑器的特效和技能编辑器的技能，创建完整的游戏单位。采用引用式设计，确保资源的复用性和一致性。

## 2. 核心功能

### 2.1 资源引用系统
```typescript
interface ResourceReferenceSystem {
  // === 模型引用 ===
  modelReference: {
    // 模型选择
    modelSelection: {
      // 浏览模型库
      modelBrowser: {
        categories: string[]                 // 模型分类
        searchQuery: string                  // 搜索关键词
        filterTags: string[]                 // 标签过滤
        sortBy: 'name' | 'date' | 'popularity' | 'compatibility'
        
        // 预览功能
        preview: {
          show3DPreview: boolean
          showWireframe: boolean
          showBoundingBox: boolean
          rotateModel: boolean
        }
      }
      
      // 兼容性检查
      compatibilityCheck: {
        animationCompatibility: CompatibilityResult[]
        effectCompatibility: CompatibilityResult[]
        performanceImpact: PerformanceEstimate
        recommendations: string[]
      }
    }
    
    // 模型配置
    modelConfiguration: {
      // 基础设置
      basicSettings: {
        scale: { x: number, y: number, z: number }
        rotation: { x: number, y: number, z: number }
        position: { x: number, y: number, z: number }
        
        // 材质覆盖
        materialOverrides: {
          enabled: boolean
          overrides: { [partName: string]: MaterialSettings }
        }
      }
      
      // 高级设置
      advancedSettings: {
        lodSettings: {
          enabled: boolean
          distances: number[]
          qualityLevels: number[]
        }
        
        cullingSettings: {
          frustumCulling: boolean
          occlusionCulling: boolean
          distanceCulling: number
        }
      }
    }
  }
  
  // === 动画引用 ===
  animationReference: {
    // 动画分配
    animationAssignment: {
      // 基础动画槽
      basicSlots: {
        idle: { animationId: string, priority: number, loop: boolean }
        walk: { animationId: string, priority: number, loop: boolean }
        run: { animationId: string, priority: number, loop: boolean }
        attack: { animationId: string, priority: number, loop: boolean }
        death: { animationId: string, priority: number, loop: boolean }
        hit: { animationId: string, priority: number, loop: boolean }
      }
      
      // 扩展动画槽
      extendedSlots: {
        [slotName: string]: {
          animationId: string
          priority: number
          loop: boolean
          conditions: AnimationCondition[]
        }
      }
      
      // 动画混合
      blendingSettings: {
        enabled: boolean
        blendTime: number
        blendMode: 'linear' | 'smooth' | 'custom'
        
        // 状态机
        stateMachine: {
          states: AnimationState[]
          transitions: AnimationTransition[]
          defaultState: string
        }
      }
    }
    
    // 动画参数映射
    parameterMapping: {
      // 属性到动画参数的映射
      attributeMapping: {
        moveSpeed: {
          targetAnimation: 'walk'
          targetParameter: 'speed'
          mappingCurve: AnimationCurve
        }
        
        attackSpeed: {
          targetAnimation: 'attack'
          targetParameter: 'playbackSpeed'
          mappingCurve: AnimationCurve
        }
        
        health: {
          targetAnimation: 'idle'
          targetParameter: 'intensity'
          mappingCurve: AnimationCurve
          inverted: true  // 血量越低强度越高
        }
      }
      
      // 状态到动画的映射
      statusMapping: {
        stunned: { animation: 'stunned', priority: 100 }
        poisoned: { animation: 'poisoned_idle', blendWith: 'idle' }
        burning: { animation: 'burning_dance', priority: 80 }
        frozen: { animation: 'frozen_idle', speedMultiplier: 0.1 }
      }
    }
  }
  
  // === 特效引用 ===
  effectReference: {
    // 特效分配
    effectAssignment: {
      // 状态特效
      statusEffects: {
        idle: { effectId: string, attachPoint: string, loop: boolean }
        moving: { effectId: string, attachPoint: string, loop: boolean }
        attacking: { effectId: string, attachPoint: string, loop: boolean }
        casting: { effectId: string, attachPoint: string, loop: boolean }
        dying: { effectId: string, attachPoint: string, loop: boolean }
      }
      
      // 事件特效
      eventEffects: {
        onSpawn: { effectId: string, position: 'unit' | 'ground', duration: number }
        onDeath: { effectId: string, position: 'unit' | 'ground', duration: number }
        onLevelUp: { effectId: string, position: 'unit', duration: number }
        onCriticalHit: { effectId: string, position: 'target', duration: number }
        onAbilityUse: { [abilityId: string]: { effectId: string, timing: 'start' | 'end' | 'impact' } }
      }
      
      // 环境特效
      environmentEffects: {
        aura: { effectId: string, radius: number, followUnit: boolean }
        trail: { effectId: string, enabled: boolean, fadeTime: number }
        shadow: { effectId: string, groundProjection: boolean }
        footsteps: { effectId: string, terrainAdaptive: boolean }
      }
    }
    
    // 特效参数
    effectParameters: {
      // 全局设置
      globalSettings: {
        effectScale: number
        effectIntensity: number
        colorTint: Color
        
        // 性能设置
        maxSimultaneousEffects: number
        effectLOD: boolean
        distanceCulling: number
      }
      
      // 条件特效
      conditionalEffects: {
        lowHealth: { threshold: 30, effectId: string }
        highSpeed: { threshold: 500, effectId: string }
        inCombat: { effectId: string, fadeInTime: number }
        nearAllies: { distance: 300, effectId: string }
      }
    }
  }
  
  // === 技能引用 ===
  skillReference: {
    // 技能槽配置
    skillSlots: {
      // 主动技能槽
      activeSkills: {
        slot1: { skillId: string, hotkey: string, autocast: boolean }
        slot2: { skillId: string, hotkey: string, autocast: boolean }
        slot3: { skillId: string, hotkey: string, autocast: boolean }
        slot4: { skillId: string, hotkey: string, autocast: boolean }
        ultimate: { skillId: string, hotkey: string, levelRequirement: number }
      }
      
      // 被动技能槽
      passiveSkills: {
        passive1: { skillId: string, alwaysActive: boolean }
        passive2: { skillId: string, alwaysActive: boolean }
        passive3: { skillId: string, alwaysActive: boolean }
      }
      
      // 光环技能
      auraSkills: {
        aura1: { skillId: string, radius: number, affectsAllies: boolean }
        aura2: { skillId: string, radius: number, affectsAllies: boolean }
      }
    }
    
    // 技能升级
    skillProgression: {
      // 升级方式
      progressionType: 'level_based' | 'point_based' | 'item_based' | 'quest_based'
      
      // 等级需求
      levelRequirements: {
        [skillId: string]: {
          unlockLevel: number
          maxLevel: number
          levelUpCost: number | 'auto'
        }
      }
      
      // 技能树
      skillTree: {
        enabled: boolean
        dependencies: { [skillId: string]: string[] }  // 前置技能
        mutualExclusive: string[][]                    // 互斥技能组
      }
    }
  }
}
```

### 2.2 单位属性系统 (整合版)
```typescript
interface IntegratedUnitAttributes {
  // === 基础属性 (从之前的规格整合) ===
  coreAttributes: {
    // 生命系统
    health: {
      maxHealth: number
      healthRegen: number
      armor: number
      armorType: ArmorType
      magicResist: number
      
      // 护盾系统
      shield: {
        maxShield: number
        shieldRegen: number
        shieldRegenDelay: number
      }
    }
    
    // 法力系统
    mana: {
      maxMana: number
      manaRegen: number
      manaType: ManaType
      
      // 次要资源
      secondaryResource: {
        type: SecondaryResourceType
        maxValue: number
        regenRate: number
      }
    }
    
    // 攻击系统
    attack: {
      damage: { min: number, max: number }
      attackSpeed: number
      attackRange: number
      attackType: AttackType
      weaponType: WeaponType
      
      // 暴击系统
      criticalChance: number
      criticalMultiplier: number
      
      // 特殊攻击
      multiAttack: MultiAttackSettings
      attackEffects: AttackEffectSettings
    }
    
    // 移动系统
    movement: {
      moveSpeed: number
      turnRate: number
      movementType: MovementType
      
      // 特殊移动
      canFly: boolean
      flySpeed: number
      canSwim: boolean
      swimSpeed: number
      
      // 地形适应
      terrainPassability: TerrainPassabilitySettings
    }
    
    // 感知系统
    perception: {
      sightRange: number
      nightVision: boolean
      truesight: boolean
      detectInvisible: boolean
    }
  }

  // === AI行为引用 ===
  aiBehaviorReference: {
    // AI模板选择
    aiTemplateSelection: {
      // 预设AI模板
      presetTemplates: {
        passive: { description: '被动AI，不主动攻击' }
        defensive: { description: '防御AI，被攻击时反击' }
        aggressive: { description: '主动攻击AI' }
        patrol: { description: '巡逻AI' }
        guard: { description: '守卫AI' }
        custom: { description: '自定义AI行为' }
      }

      // AI参数调整
      aiParameters: {
        aggressionLevel: number
        alertness: number
        cooperationLevel: number
        fleeThreshold: number
        pursuitDistance: number

        // 技能使用AI
        skillUsageAI: {
          useSkillsIntelligently: boolean
          skillPriorities: { [skillId: string]: number }
          conserveMana: boolean
          emergencySkillsOnly: boolean
        }
      }
    }

    // 行为状态机
    behaviorStateMachine: {
      states: {
        idle: { duration: [number, number], transitions: string[] }
        patrol: { patrolRadius: number, transitions: string[] }
        chase: { maxDistance: number, transitions: string[] }
        attack: { attackDuration: number, transitions: string[] }
        flee: { fleeDistance: number, transitions: string[] }
        return: { returnSpeed: number, transitions: string[] }
      }

      // 状态转换条件
      transitionConditions: {
        [transitionName: string]: {
          condition: string
          probability: number
          cooldown: number
        }
      }
    }
  }

  // === 经济系统引用 ===
  economicSystemReference: {
    // 战利品配置
    lootConfiguration: {
      // 掉落表引用
      lootTableReference: {
        primaryLootTable: string
        secondaryLootTables: string[]

        // 掉落修正
        lootModifiers: {
          dropRateMultiplier: number
          qualityBonus: number
          rarityBonus: number
          playerLevelScaling: boolean
        }
      }

      // 经验奖励
      experienceReward: {
        baseExperience: number
        experienceScaling: ExperienceScalingSettings
        skillExperienceRewards: { [skillType: string]: number }
      }
    }

    // 价值系统
    valueSystem: {
      economicValue: number
      rarityLevel: RarityLevel
      difficultyRating: DifficultyRating

      // 市场价值
      marketValue: {
        buyPrice: number
        sellPrice: number
        tradeValue: number
      }
    }
  }
}
