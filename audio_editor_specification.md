# 音频编辑器规格文档

## 1. 概述

音频编辑器是模块化系统的第6个专业模块，专门用于创建和编辑游戏音频资源。支持MIDI音乐和音效的自动生成、多种编辑模式、MML文本导入，以及与其他模块的无缝集成。

## 2. 核心功能

### 2.1 双模式编辑 + 五线谱预览系统
```typescript
interface DualModeEditingSystem {
  // === 主要编辑模式 ===
  editingModes: {
    // 简化模式 (新手友好)
    simplified: {
      // 风格预设
      stylePresets: {
        happy: { tempo: [120, 140], key: 'C_major', mood: 'bright', instruments: ['piano', 'strings', 'flute'] }
        heavy: { tempo: [80, 100], key: 'D_minor', mood: 'dark', instruments: ['organ', 'brass', 'timpani'] }
        heroic: { tempo: [100, 120], key: 'Bb_major', mood: 'epic', instruments: ['brass', 'strings', 'choir'] }
        mysterious: { tempo: [60, 90], key: 'F#_minor', mood: 'ambient', instruments: ['pad', 'harp', 'flute'] }
        peaceful: { tempo: [70, 90], key: 'G_major', mood: 'calm', instruments: ['piano', 'strings', 'harp'] }
        tense: { tempo: [110, 130], key: 'C_minor', mood: 'suspense', instruments: ['strings', 'brass', 'percussion'] }
        victory: { tempo: [130, 150], key: 'D_major', mood: 'triumphant', instruments: ['brass', 'timpani', 'choir'] }
        sad: { tempo: [60, 80], key: 'A_minor', mood: 'melancholy', instruments: ['piano', 'strings', 'oboe'] }
      }

      // 情绪控制滑块
      moodSliders: {
        happiness: { range: [0, 100], affects: ['tempo', 'key', 'rhythm'] }
        tension: { range: [0, 100], affects: ['dissonance', 'dynamics', 'rhythm'] }
        epicness: { range: [0, 100], affects: ['orchestration', 'dynamics', 'harmony'] }
        energy: { range: [0, 100], affects: ['tempo', 'rhythm', 'articulation'] }
        darkness: { range: [0, 100], affects: ['key', 'harmony', 'timbre'] }
      }

      // 自动生成参数
      autoGeneration: {
        duration: { min: 10, max: 300, default: 60 }  // 秒
        complexity: { simple: 1, moderate: 2, complex: 3 }
        variation: { low: 1, medium: 2, high: 3 }
        loopable: boolean
        fadeInOut: boolean
      }
    }

    // 钢琴卷帘模式 (专业编辑)
    pianoRoll: {
      // 轨道系统
      trackSystem: {
        maxTracks: 16
        trackTypes: {
          melody: { color: '#FF6B6B', defaultInstrument: 'piano' }
          harmony: { color: '#4ECDC4', defaultInstrument: 'strings' }
          bass: { color: '#45B7D1', defaultInstrument: 'bass' }
          percussion: { color: '#96CEB4', defaultInstrument: 'drums' }
          effects: { color: '#FFEAA7', defaultInstrument: 'pad' }
        }

        // 轨道属性
        trackProperties: {
          volume: { range: [0, 127], default: 100 }
          pan: { range: [-64, 64], default: 0 }
          mute: boolean
          solo: boolean
          instrument: InstrumentType
          effects: EffectChain[]
        }
      }

      // 音符编辑
      noteEditing: {
        // 音符属性
        noteProperties: {
          pitch: { range: ['C0', 'G9'] }
          velocity: { range: [1, 127] }
          duration: { min: 0.125, max: 16 }  // 以拍为单位
          timing: number  // 精确到tick
        }

        // 编辑工具
        editingTools: {
          pencil: { description: '绘制音符' }
          eraser: { description: '删除音符' }
          select: { description: '选择音符' }
          cut: { description: '剪切工具' }

          // 批量操作
          batchOperations: {
            quantize: { grid: [0.25, 0.5, 1, 2] }
            transpose: { semitones: number }
            velocityScale: { factor: number }
            timeStretch: { factor: number }
          }
        }
      }

      // 时间轴和网格
      timeline: {
        timeSignature: { numerator: number, denominator: number }
        tempo: { bpm: number, changes: TempoChange[] }
        grid: { subdivision: number, snap: boolean }
        zoom: { horizontal: number, vertical: number }
      }
    }
  }

  // === 五线谱预览窗口 (只读显示) ===
  staffPreviewWindow: {
    // 显示功能
    displayFeatures: {
      realTimeUpdate: boolean              // 实时更新显示
      multiTrackDisplay: boolean           // 多轨道显示
      scrollableView: boolean              // 可滚动查看
      zoomControl: boolean                 // 缩放控制
    }

    // 显示设置
    displaySettings: {
      clef: 'treble' | 'bass' | 'grand_staff'  // 谱号选择
      keySignature: KeySignature           // 调号显示
      timeSignature: TimeSignature         // 拍号显示
      showMeasureNumbers: boolean          // 显示小节号
      showChordSymbols: boolean            // 显示和弦标记
    }

    // 导出功能
    exportFeatures: {
      // 图片导出
      imageExport: {
        formats: ['PNG', 'JPG', 'SVG', 'PDF']
        resolution: [72, 150, 300, 600]     // DPI选项
        paperSize: ['A4', 'Letter', 'Custom']
        orientation: 'portrait' | 'landscape'
      }

      // 打印设置
      printSettings: {
        pageLayout: boolean
        margins: { top: number, bottom: number, left: number, right: number }
        title: string
        composer: string
        copyright: string
      }
    }

    // 交互功能 (有限)
    limitedInteraction: {
      clickToPlay: boolean                 // 点击播放对应位置
      selectionHighlight: boolean         // 高亮选中区域
      measureNavigation: boolean          // 小节导航
      noEditing: true                      // 明确标记：不支持编辑
    }
  }
}
```

### 2.2 MIDI生成引擎
```typescript
interface MIDIGenerationEngine {
  // === 音乐生成算法 ===
  musicGeneration: {
    // 旋律生成
    melodyGeneration: {
      // 生成算法
      algorithms: {
        markov: { order: number, corpus: string[] }
        neural: { model: string, temperature: number }
        rule_based: { rules: CompositionRule[] }
        genetic: { population: number, generations: number }
      }
      
      // 旋律特征
      melodyFeatures: {
        range: { min: string, max: string }  // 音域
        direction: 'ascending' | 'descending' | 'mixed'
        stepwise: number  // 级进比例 0-1
        leaps: number     // 跳进比例 0-1
        repetition: number  // 重复比例 0-1
        sequence: boolean   // 是否使用模进
      }
      
      // 节奏模式
      rhythmPatterns: {
        simple: [1, 1, 1, 1]  // 四分音符
        syncopated: [1, 0.5, 0.5, 1]  // 切分音
        dotted: [1.5, 0.5, 1, 1]  // 附点节奏
        triplet: [0.33, 0.33, 0.33, 1]  // 三连音
        complex: [1, 0.25, 0.25, 0.5, 1]  // 复杂节奏
      }
    }
    
    // 和声生成
    harmonyGeneration: {
      // 和弦进行
      chordProgressions: {
        classical: ['I', 'vi', 'IV', 'V']
        pop: ['vi', 'IV', 'I', 'V']
        jazz: ['IIM7', 'V7', 'IM7', 'VIM7']
        modal: ['i', 'VII', 'VI', 'VII']
        epic: ['i', 'VI', 'III', 'VII']
      }
      
      // 和声节奏
      harmonicRhythm: {
        slow: 4    // 每4拍换和弦
        medium: 2  // 每2拍换和弦
        fast: 1    // 每拍换和弦
        mixed: 'variable'  // 混合节奏
      }
      
      // 声部进行
      voiceLeading: {
        smoothness: number  // 声部平滑度 0-1
        parallelMotion: number  // 平行进行比例
        contraryMotion: number  // 反向进行比例
        obliqueMotion: number   // 斜向进行比例
      }
    }
    
    // 编曲生成
    orchestration: {
      // 乐器分配
      instrumentAssignment: {
        melody: InstrumentType[]
        harmony: InstrumentType[]
        bass: InstrumentType[]
        percussion: InstrumentType[]
        effects: InstrumentType[]
      }
      
      // 织体类型
      textureTypes: {
        monophonic: { voices: 1 }
        homophonic: { melody: 1, accompaniment: 'multiple' }
        polyphonic: { voices: 'multiple', independence: 'high' }
        heterophonic: { variations: 'multiple' }
      }
      
      // 动态变化
      dynamics: {
        overall: DynamicLevel
        changes: DynamicChange[]
        crescendo: boolean
        diminuendo: boolean
      }
    }
  }
  
  // === 音效生成 ===
  soundEffectGeneration: {
    // 环境音效
    environmentalSounds: {
      cave: {
        waterDrip: {
          frequency: { min: 0.5, max: 3.0 }  // 每秒滴水次数
          pitch: { base: 'C5', variation: 2 }  // 基础音高和变化范围
          reverb: { size: 'large', decay: 'long' }
          echo: { delay: 0.3, feedback: 0.4 }
        }
        
        wind: {
          intensity: { min: 0.2, max: 0.8 }
          frequency: { base: 60, variation: 20 }  // Hz
          filter: { type: 'lowpass', cutoff: 200 }
          modulation: { rate: 0.1, depth: 0.3 }
        }
        
        footsteps: {
          surface: 'stone' | 'water' | 'gravel'
          pace: { slow: 0.8, normal: 1.2, fast: 2.0 }  // 步/秒
          weight: { light: 0.5, normal: 1.0, heavy: 1.5 }
          echo: { enabled: boolean, intensity: number }
        }
      }
      
      fire: {
        crackling: {
          intensity: { small: 0.3, medium: 0.6, large: 1.0 }
          frequency: { base: 100, variation: 50 }
          noise: { type: 'pink', amount: 0.7 }
          modulation: { rate: 5, depth: 0.4 }
        }
        
        roaring: {
          size: 'small' | 'medium' | 'large' | 'inferno'
          lowFreq: { base: 40, variation: 20 }
          highFreq: { base: 2000, variation: 500 }
          dynamics: { min: 0.4, max: 0.9 }
        }
      }
      
      water: {
        flowing: {
          speed: 'trickle' | 'stream' | 'river' | 'rapids'
          frequency: { base: 200, variation: 100 }
          noise: { type: 'white', filtered: true }
          stereo: { width: number, movement: boolean }
        }
        
        splash: {
          size: 'small' | 'medium' | 'large'
          pitch: { base: 'C4', decay: 'fast' }
          noise: { burst: 'short', tail: 'medium' }
          reverb: { size: 'medium', predelay: 0.02 }
        }
      }
    }
    
    // 战斗音效
    combatSounds: {
      weapons: {
        sword: {
          swing: { pitch: 'C3', noise: 'whoosh', duration: 0.3 }
          hit: { pitch: 'C2', noise: 'clang', duration: 0.1 }
          block: { pitch: 'F2', noise: 'metal', duration: 0.2 }
        }
        
        bow: {
          draw: { pitch: 'G3', noise: 'creak', duration: 0.5 }
          release: { pitch: 'C4', noise: 'snap', duration: 0.1 }
          arrow_flight: { pitch: 'A4', noise: 'whistle', duration: 1.0 }
        }
        
        magic: {
          charge: { pitch: 'C5', harmonics: true, duration: 1.0 }
          cast: { pitch: 'G5', sparkle: true, duration: 0.3 }
          impact: { pitch: 'C4', explosion: true, duration: 0.5 }
        }
      }
      
      impacts: {
        hit_flesh: { pitch: 'C2', noise: 'thud', duration: 0.2 }
        hit_armor: { pitch: 'F2', noise: 'clang', duration: 0.3 }
        hit_shield: { pitch: 'Bb2', noise: 'block', duration: 0.25 }
        critical_hit: { pitch: 'C3', sparkle: true, duration: 0.4 }
      }
    }
    
    // UI音效
    interfaceSounds: {
      buttons: {
        click: { pitch: 'C5', duration: 0.1, type: 'clean' }
        hover: { pitch: 'G4', duration: 0.05, type: 'soft' }
        confirm: { pitch: 'C5', harmony: 'major', duration: 0.3 }
        cancel: { pitch: 'F4', harmony: 'minor', duration: 0.2 }
        error: { pitch: 'C3', dissonance: true, duration: 0.4 }
      }
      
      notifications: {
        achievement: { melody: 'victory_fanfare', duration: 2.0 }
        level_up: { melody: 'ascending_arp', duration: 1.5 }
        item_pickup: { pitch: 'C5', sparkle: true, duration: 0.3 }
        quest_complete: { melody: 'completion_chord', duration: 1.0 }
      }
    }
  }
}
```

### 2.3 MML文本导入系统
```typescript
interface MMLImportSystem {
  // === MML解析器 ===
  mmlParser: {
    // 支持的MML格式
    supportedFormats: {
      standard: {
        notes: 'A-G'
        octaves: '0-8'
        durations: '1,2,4,8,16,32,64'
        modifiers: '#,+,-,.'
        commands: 'T,L,O,V,@'
      }

      extended: {
        loops: '[',']'
        macros: '{}','()'
        effects: 'q,s,h,u,d'
        panning: 'p'
        detune: 'D'
        portamento: '&'
      }
    }

    // 解析规则
    parsingRules: {
      // 音符解析
      notePattern: /([A-G][#+-]?)(\d*)(\.*)/g

      // 命令解析
      commandPattern: /([TLOVD@pqshu])(\d+)/g

      // 循环解析
      loopPattern: /\[([^\[\]]*)\](\d*)/g

      // 宏解析
      macroPattern: /\{([^{}]*)\}/g
    }

    // 错误处理
    errorHandling: {
      syntaxErrors: {
        invalidNote: '无效音符'
        invalidOctave: '无效八度'
        invalidDuration: '无效时值'
        unmatchedBrackets: '括号不匹配'
      }

      warnings: {
        outOfRange: '音符超出范围'
        tempoChange: '速度变化过快'
        volumeClipping: '音量过大'
      }

      autoCorrection: {
        enabled: boolean
        clampValues: boolean
        suggestAlternatives: boolean
      }
    }
  }

  // === MML示例模板 ===
  mmlTemplates: {
    // 基础模板
    basic: {
      scale: 'CDEFGAB>C'
      arpeggio: 'CEG>C<GEC'
      chromatic: 'CC#DD#EFF#GG#AA#B>C'
    }

    // 风格模板
    styles: {
      classical: {
        minuet: 'T120 L4 G>D<B>D<G>D<B>D'
        waltz: 'T180 L4 C.E.G.>C.<G.E.C.'
        march: 'T120 L4 CC GG AA G'
      }

      game_music: {
        victory: 'T140 L8 >C<G>C<G>E<G>E<G>G4'
        battle: 'T160 L16 GGGG AAAA BBBB >CCCC'
        peaceful: 'T80 L2 C E G >C'
        mysterious: 'T60 L4 F# A C# F#'
      }

      sound_effects: {
        coin: 'T200 L32 >B>F#'
        jump: 'T300 L64 C>C>>C'
        explosion: 'T100 L128 CDEFGAB>CDEFGAB'
        powerup: 'T150 L16 CEG>CEG>C'
      }
    }

    // 复杂示例
    advanced: {
      multi_channel: {
        melody: 'A: T120 L4 CDEFGAB>C',
        harmony: 'B: T120 L2 C E G >C',
        bass: 'C: T120 L1 C'
      }

      with_effects: {
        reverb: 'h64 CDEFGAB>C h0',
        vibrato: 'u4,4 CDEFGAB>C u0',
        portamento: 'C&D&E&F&G&A&B&>C'
      }
    }
  }

  // === 乐器映射 ===
  instrumentMapping: {
    // GM标准乐器
    generalMIDI: {
      piano: { program: 1, name: '大钢琴' }
      organ: { program: 17, name: '管风琴' }
      guitar: { program: 25, name: '吉他' }
      bass: { program: 34, name: '贝斯' }
      strings: { program: 49, name: '弦乐' }
      brass: { program: 57, name: '铜管' }
      flute: { program: 74, name: '长笛' }
      drums: { program: 128, name: '鼓组' }
    }

    // 游戏音色
    gameInstruments: {
      chiptune: {
        square: { waveform: 'square', duty: 0.5 }
        triangle: { waveform: 'triangle' }
        noise: { waveform: 'noise', type: 'white' }
        sawtooth: { waveform: 'sawtooth' }
      }

      fantasy: {
        harp: { program: 47, reverb: 0.3 }
        choir: { program: 53, reverb: 0.5 }
        bell: { program: 15, decay: 'long' }
        pad: { program: 89, attack: 'slow' }
      }
    }

    // 自定义音色
    customInstruments: {
      // 用户可以定义自己的音色
      userDefined: InstrumentDefinition[]
    }
  }
}
```

### 2.4 优化后的用户界面设计
```typescript
interface OptimizedAudioEditorUI {
  // === 主界面布局 (双模式 + 五线谱预览) ===
  mainInterface: {
    layout: `
    ┌─────────────────────────────────────────────────────────────┐
    │ 菜单栏: 文件 编辑 音频 工具 导入 导出 帮助                   │
    ├─────────────────────────────────────────────────────────────┤
    │ 工具栏: [新建] [打开] [保存] [播放] [录制] [五线谱] [导出]   │
    ├──────────┬──────────────────────────┬─────────────────────────┤
    │          │                          │                         │
    │ 模式选择 │     主编辑区域            │    属性面板              │
    │          │                          │                         │
    │ ○简化模式│  ┌─────────────────────┐ │ ┌─音频属性─┐            │
    │ ●钢琴卷帘│  │                     │ │ ├─乐器设置─┤            │
    │          │  │    [编辑界面]        │ │ ├─效果器──┤            │
    │ 模板库   │  │                     │ │ ├─MML导入─┤            │
    │ ┌风格模板┐│  └─────────────────────┘ │ └─导出设置─┘            │
    │ ├音效模板┤│                          │                         │
    │ ├MML模板┤│  ┌─────────────────────┐ │    预览面板              │
    │ └用户库─┘│  │   [波形显示/频谱]    │ │ ┌─播放控制─┐            │
    │          │  └─────────────────────┘ │ ├─音量表──┤            │
    │          │                          │ ├─频谱分析─┤            │
    │          │  [五线谱预览] 按钮        │ └─导出预览─┘            │
    ├──────────┴──────────────────────────┴─────────────────────────┤
    │ 状态栏: 当前模式 | 播放时间 | 采样率 | CPU使用率              │
    └─────────────────────────────────────────────────────────────┘
    `
  }

  // === 五线谱预览窗口 (独立弹窗) ===
  staffPreviewWindow: {
    windowLayout: `
    ┌─────────────────────────────────────────────────────────────┐
    │ 五线谱预览 - [最小化] [最大化] [关闭]                        │
    ├─────────────────────────────────────────────────────────────┤
    │ 工具栏: [刷新] [缩放+] [缩放-] [适应窗口] [导出图片] [打印]  │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  ♪ = 120                    C大调                4/4拍      │
    │                                                             │
    │  ────────────────────────────────────────────────────────  │
    │  │ ♪ ♪ ♫ ♪ │ ♪ ♪ ♫ ♪ │ ♪ ♪ ♫ ♪ │ ♪ ♪ ♫ ♪ │              │
    │  ────────────────────────────────────────────────────────  │
    │                                                             │
    │  ────────────────────────────────────────────────────────  │
    │  │ ♩   ♩   │ ♩   ♩   │ ♩   ♩   │ ♩   ♩   │              │
    │  ────────────────────────────────────────────────────────  │
    │                                                             │
    │                    [点击任意位置播放]                        │
    │                                                             │
    ├─────────────────────────────────────────────────────────────┤
    │ 显示设置: [谱号] [调号] [拍号] [小节号] [和弦标记]           │
    │ 导出设置: [PNG] [PDF] [A4] [300DPI] [导出] [打印]          │
    └─────────────────────────────────────────────────────────────┘
    `

    // 窗口特性
    windowFeatures: {
      resizable: boolean                   // 可调整大小
      alwaysOnTop: boolean                 // 置顶显示选项
      realTimeSync: boolean                // 与主编辑器实时同步
      independentScroll: boolean           // 独立滚动
    }

    // 显示控制
    displayControls: {
      zoomLevels: [50, 75, 100, 125, 150, 200]  // 缩放级别
      fitToWindow: boolean                 // 适应窗口
      pageView: boolean                    // 分页显示
      continuousView: boolean              // 连续显示
    }

    // 交互功能
    interactions: {
      clickToPlay: {
        enabled: boolean
        highlightPlayback: boolean         // 播放时高亮
        followPlayback: boolean            // 跟随播放位置
      }

      measureNavigation: {
        enabled: boolean
        jumpToMeasure: boolean             // 跳转到指定小节
        nextPrevious: boolean              // 上一个/下一个小节
      }

      selectionSync: {
        enabled: boolean
        highlightSelection: boolean        // 高亮主编辑器中的选择
        bidirectionalSync: boolean         // 双向同步选择
      }
    }
  }

  // === 简化的模式切换 ===
  simplifiedModeSwitch: {
    // 简化模式界面
    simplifiedMode: {
      styleSelector: {
        categories: ['情绪', '场景', '战斗', '环境']
        previewButton: boolean
        customization: {
          tempo: SliderControl
          key: DropdownControl
          instruments: CheckboxGroup
          duration: NumberInput
        }
      }

      moodControls: {
        sliders: MoodSlider[]
        realTimePreview: boolean
        autoGenerate: ButtonControl
      }
    }

    // 钢琴卷帘模式界面
    pianoRollMode: {
      trackPanel: {
        trackList: TrackListComponent
        addTrack: ButtonControl
        trackMixer: MixerComponent
      }

      editingArea: {
        pianoKeys: PianoKeyComponent
        noteGrid: NoteGridComponent
        timeline: TimelineComponent
        tools: EditingToolsComponent
      }
    }
  }

  // === MML编辑器 (集成到属性面板) ===
  integratedMMLEditor: {
    // 文本编辑区
    textEditor: {
      syntaxHighlighting: boolean
      autoCompletion: boolean
      errorHighlighting: boolean
      lineNumbers: boolean
      foldableBlocks: boolean              // 可折叠代码块
    }

    // 快速插入
    quickInsert: {
      templateDropdown: TemplateSelector
      noteButtons: NoteButtonGrid         // 音符快速输入按钮
      commandButtons: CommandButtonGrid   // 命令快速输入
    }

    // 实时验证
    validation: {
      realTimeCheck: boolean
      errorTooltips: boolean
      autoCorrection: boolean
      suggestionPopup: boolean
    }
  }
}
```

### 2.5 五线谱渲染系统 (简化实现)
```typescript
interface StaffRenderingSystem {
  // === 渲染引擎 ===
  renderingEngine: {
    // 基础渲染组件
    basicComponents: {
      // 五线谱基础
      staffLines: {
        lineCount: 5
        lineSpacing: number                // 线间距
        lineThickness: number              // 线粗细
        color: string                      // 线颜色
      }

      // 谱号渲染
      clefRendering: {
        trebleClef: SVGPath                // 高音谱号SVG路径
        bassClef: SVGPath                  // 低音谱号SVG路径
        position: { x: number, y: number }
        size: number
      }

      // 调号渲染
      keySignatureRendering: {
        sharps: SVGPath[]                  // 升号位置
        flats: SVGPath[]                   // 降号位置
        positions: { [key: string]: number[] }  // 各调号的位置
      }

      // 拍号渲染
      timeSignatureRendering: {
        numerator: number
        denominator: number
        position: { x: number, y: number }
        fontSize: number
      }
    }

    // 音符渲染
    noteRendering: {
      // 音符符头
      noteHeads: {
        whole: SVGPath                     // 全音符
        half: SVGPath                      // 二分音符
        quarter: SVGPath                   // 四分音符
        filled: SVGPath                    // 实心符头
        hollow: SVGPath                    // 空心符头
      }

      // 符干和符尾
      stemsAndBeams: {
        stemLength: number                 // 符干长度
        stemThickness: number              // 符干粗细
        beamThickness: number              // 符尾粗细
        beamSlope: number                  // 符尾倾斜度
      }

      // 音符位置计算
      notePositioning: {
        // Y轴位置 (音高)
        pitchToY: (pitch: string) => number

        // X轴位置 (时间)
        timeToX: (time: number) => number

        // 自动间距
        autoSpacing: {
          minNoteDistance: number          // 最小音符间距
          measureWidth: number             // 小节宽度
          adaptiveSpacing: boolean         // 自适应间距
        }
      }
    }

    // 简化的布局算法
    simplifiedLayout: {
      // 不实现复杂的音符排版
      noCollisionDetection: true          // 不检测音符碰撞
      noAutoBeaming: true                  // 不自动连梁
      noVoiceSeparation: true              // 不分离声部

      // 只实现基础布局
      basicLayout: {
        fixedMeasureWidth: boolean         // 固定小节宽度
        simpleSpacing: boolean             // 简单等距间距
        noOverlap: boolean                 // 避免重叠
      }
    }
  }

  // === 数据转换 ===
  dataConversion: {
    // 从钢琴卷帘数据转换
    fromPianoRoll: {
      // 音符转换
      noteConversion: {
        midiToStaff: (midiNote: number) => StaffPosition
        durationToNotation: (duration: number) => NoteType
        velocityToDynamics: (velocity: number) => DynamicLevel
      }

      // 时间转换
      timeConversion: {
        ticksToMeasures: (ticks: number) => { measure: number, beat: number }
        tempoToSpacing: (tempo: number) => number
        quantizeToGrid: (time: number) => number
      }

      // 轨道转换
      trackConversion: {
        singleTrackToStaff: (track: Track) => StaffData
        multiTrackToGrandStaff: (tracks: Track[]) => GrandStaffData
        instrumentToClef: (instrument: string) => ClefType
      }
    }

    // 从MML数据转换
    fromMML: {
      mmlToStaffData: (mml: string) => StaffData
      parseCommands: (commands: string[]) => StaffSettings
      handleLoops: (loopData: LoopData) => StaffData
    }
  }

  // === 导出功能 ===
  exportFeatures: {
    // 图片导出
    imageExport: {
      // 渲染到Canvas
      canvasRendering: {
        width: number
        height: number
        dpi: number
        backgroundColor: string
      }

      // 格式转换
      formatConversion: {
        toPNG: (canvas: HTMLCanvasElement) => Blob
        toJPEG: (canvas: HTMLCanvasElement) => Blob
        toSVG: (staffData: StaffData) => string
        toPDF: (staffData: StaffData) => Blob
      }

      // 打印布局
      printLayout: {
        paperSize: 'A4' | 'Letter' | 'Legal'
        orientation: 'portrait' | 'landscape'
        margins: { top: number, right: number, bottom: number, left: number }

        // 页面信息
        pageInfo: {
          title: string
          composer: string
          copyright: string
          pageNumbers: boolean
        }
      }
    }
  }

  // === 性能优化 ===
  performanceOptimization: {
    // 渲染优化
    renderingOptimization: {
      virtualScrolling: boolean           // 虚拟滚动
      lazyRendering: boolean              // 延迟渲染
      cacheRendering: boolean             // 缓存渲染结果
      levelOfDetail: boolean              // 细节层次
    }

    // 内存优化
    memoryOptimization: {
      objectPooling: boolean              // 对象池
      textureAtlas: boolean               // 纹理图集
      geometryInstancing: boolean         // 几何实例化
    }
  }
}
```

## 3. 系统集成

### 3.1 与其他模块的集成
```typescript
interface SystemIntegration {
  // === 技能编辑器集成 ===
  skillEditorIntegration: {
    // 技能音效绑定
    skillAudioBinding: {
      castSound: { audioId: string, timing: 'start' | 'channel' | 'complete' }
      impactSound: { audioId: string, timing: 'on_hit' | 'on_crit' }
      ambientSound: { audioId: string, loop: boolean, fadeIn: number }
    }

    // 动态音效
    dynamicAudio: {
      parameterMapping: {
        skillLevel: { affects: 'pitch' | 'volume' | 'reverb' }
        damage: { affects: 'intensity' | 'distortion' }
        criticalHit: { triggers: 'special_sound' }
      }
    }
  }

  // === 单位编辑器集成 ===
  unitEditorIntegration: {
    // 单位音效
    unitAudio: {
      voiceLines: { audioId: string, triggers: string[] }
      footsteps: { audioId: string, surface: string }
      ambientSounds: { audioId: string, conditions: string[] }
      deathSound: { audioId: string, variation: number }
    }

    // 情境音乐
    contextualMusic: {
      combatMusic: { audioId: string, intensity: number }
      idleMusic: { audioId: string, peaceful: boolean }
      victoryMusic: { audioId: string, duration: number }
    }
  }

  // === 场景系统集成 ===
  sceneIntegration: {
    // 环境音频
    environmentalAudio: {
      backgroundMusic: { audioId: string, loop: boolean, fadeTime: number }
      ambientSounds: { audioId: string, volume: number, position: '3d' | '2d' }
      weatherSounds: { audioId: string, intensity: number, weather: string }
    }

    // 音频区域
    audioZones: {
      enterSound: { audioId: string, fadeIn: number }
      exitSound: { audioId: string, fadeOut: number }
      loopSound: { audioId: string, volume: number }
    }
  }
}
```

---

## 总结

音频编辑器作为第6个专业模块，提供了：

### 核心功能
- **多模式编辑**: 简化模式、钢琴卷帘、五线谱三种模式满足不同用户
- **智能生成**: 基于风格和情绪的自动音乐生成
- **完整音效库**: 环境、战斗、UI等各类音效模板
- **MML支持**: 完整的MML文本导入和编辑功能

### 用户体验
- **渐进式学习**: 从简单到复杂的学习路径
- **实时预览**: 即时听到编辑效果
- **模板丰富**: 大量预设模板快速上手
- **智能提示**: 自动补全和错误检查

### 系统集成
- **无缝集成**: 与技能、单位、场景系统完美配合
- **动态音效**: 根据游戏状态动态调整音频
- **性能优化**: 内置音频压缩和优化
- **标准输出**: 生成标准MIDI和音频文件

这个设计既满足了专业音乐制作的需求，又保持了游戏开发的易用性！
