# 动画编辑器规格文档

## 1. 概述

动画编辑器专门用于创建基于体素的动画脚本，通过体素部位的运动控制实现各种动画效果。支持从简单的循环动画到复杂的多部位协调动画，为单位编辑器和技能编辑器提供动画资源。

## 2. 核心功能

### 2.1 动画脚本系统
```typescript
interface AnimationScriptSystem {
  // === 脚本类型 ===
  scriptTypes: {
    // 基础动画脚本
    basic: {
      loop: {
        type: 'infinite_loop'
        duration: number                     // 循环周期（秒）
        easing: 'linear' | 'ease_in' | 'ease_out' | 'ease_in_out'
        reverseOnComplete: boolean           // 完成后反向播放
      }
      
      once: {
        type: 'play_once'
        duration: number
        holdLastFrame: boolean               // 保持最后一帧
        returnToStart: boolean               // 返回起始状态
      }
      
      trigger: {
        type: 'event_triggered'
        triggerEvent: string                 // 触发事件名称
        priority: number                     // 优先级
        interruptible: boolean               // 是否可被打断
      }
    }
    
    // 复合动画脚本
    composite: {
      sequence: {
        type: 'sequential'
        animations: AnimationStep[]          // 动画序列
        transitions: TransitionSettings[]    // 过渡设置
      }
      
      parallel: {
        type: 'parallel'
        animations: AnimationTrack[]         // 并行动画轨道
        synchronization: 'start' | 'end' | 'duration'
      }
      
      conditional: {
        type: 'conditional'
        conditions: AnimationCondition[]     // 条件列表
        branches: AnimationBranch[]          // 分支动画
      }
    }
  }
  
  // === 动画参数 ===
  animationParameters: {
    // 运动参数
    motion: {
      position: {
        x: { value: number, curve: AnimationCurve }
        y: { value: number, curve: AnimationCurve }
        z: { value: number, curve: AnimationCurve }
      }
      
      rotation: {
        x: { value: number, curve: AnimationCurve }  // 度数
        y: { value: number, curve: AnimationCurve }
        z: { value: number, curve: AnimationCurve }
      }
      
      scale: {
        x: { value: number, curve: AnimationCurve }
        y: { value: number, curve: AnimationCurve }
        z: { value: number, curve: AnimationCurve }
        uniform: boolean                     // 统一缩放
      }
    }
    
    // 变形参数
    deformation: {
      bend: { axis: 'x' | 'y' | 'z', angle: number, pivot: [number, number, number] }
      twist: { axis: 'x' | 'y' | 'z', angle: number, segments: number }
      stretch: { axis: 'x' | 'y' | 'z', factor: number, falloff: number }
      squash: { factor: number, preserveVolume: boolean }
    }
    
    // 材质参数
    material: {
      color: { r: number, g: number, b: number, a: number }
      emission: number
      transparency: number
      roughness: number
      metallic: number
    }
    
    // 特效参数
    effects: {
      particles: { enabled: boolean, type: string, density: number }
      trails: { enabled: boolean, length: number, fade: number }
      glow: { enabled: boolean, intensity: number, radius: number }
      distortion: { enabled: boolean, strength: number, frequency: number }
    }
  }
}
```

### 2.2 部位绑定和控制系统
```typescript
interface BodyPartBinding {
  // === 部位识别 ===
  partIdentification: {
    // 自动识别
    autoRecognition: {
      enabled: boolean
      algorithm: 'shape_analysis' | 'connectivity' | 'user_training'
      confidence: number
      suggestions: PartSuggestion[]
    }
    
    // 手动绑定
    manualBinding: {
      // 绑定工具
      bindingTools: {
        select: { mode: 'single' | 'group' | 'hierarchy' }
        paint: { brushSize: number, feather: number }
        flood: { tolerance: number, connectivity: '6way' | '18way' | '26way' }
        lasso: { precision: number, smoothing: boolean }
      }
      
      // 部位层级
      partHierarchy: {
        root: BodyPart
        children: BodyPart[]
        parent: BodyPart | null
        siblings: BodyPart[]
        
        // 继承关系
        inheritTransform: boolean        // 继承变换
        inheritMaterial: boolean         // 继承材质
        inheritEffects: boolean          // 继承特效
      }
    }
  }
  
  // === 控制系统 ===
  controlSystem: {
    // 关节控制
    jointControl: {
      // 关节类型
      jointTypes: {
        hinge: { axis: [number, number, number], limits: [number, number] }
        ball: { limits: { x: [number, number], y: [number, number], z: [number, number] } }
        slider: { axis: [number, number, number], limits: [number, number] }
        fixed: { locked: boolean }
        free: { constraints: Constraint[] }
      }
      
      // 关节属性
      jointProperties: {
        stiffness: number                // 刚度
        damping: number                  // 阻尼
        friction: number                 // 摩擦
        elasticity: number               // 弹性
        breakForce: number               // 断裂力
      }
    }
    
    // 约束系统
    constraints: {
      // 位置约束
      positionConstraints: {
        clamp: { min: [number, number, number], max: [number, number, number] }
        distance: { target: BodyPart, distance: number, tolerance: number }
        surface: { target: Surface, offset: number }
        path: { path: Path, speed: number, loop: boolean }
      }
      
      // 旋转约束
      rotationConstraints: {
        lookAt: { target: BodyPart | Point, upVector: [number, number, number] }
        limit: { x: [number, number], y: [number, number], z: [number, number] }
        copy: { target: BodyPart, factor: number, offset: [number, number, number] }
      }
      
      // 高级约束
      advancedConstraints: {
        ik: { chain: BodyPart[], target: Point, poleVector: Point }
        spline: { controlPoints: Point[], tension: number, smoothing: number }
        physics: { gravity: boolean, collision: boolean, mass: number }
      }
    }
  }
}
```

### 2.3 动画模板库
```typescript
interface AnimationTemplateLibrary {
  // === 移动动画模板 ===
  locomotion: {
    // 双足行走
    bipedal: {
      walk: {
        name: '双足行走'
        requirements: { legs: 2, torso: 1 }
        parameters: {
          stepLength: { default: 1.0, range: [0.5, 2.0] }
          stepHeight: { default: 0.3, range: [0.1, 0.8] }
          frequency: { default: 1.0, range: [0.5, 3.0] }
          bodySwing: { default: 0.1, range: [0.0, 0.3] }
          armSwing: { default: 0.2, range: [0.0, 0.5] }
        }
        keyframes: WalkKeyframes
      }
      
      run: {
        name: '双足奔跑'
        requirements: { legs: 2, torso: 1 }
        parameters: {
          stepLength: { default: 1.5, range: [1.0, 3.0] }
          stepHeight: { default: 0.5, range: [0.2, 1.0] }
          frequency: { default: 2.0, range: [1.5, 4.0] }
          bodyLean: { default: 0.2, range: [0.0, 0.4] }
          armPump: { default: 0.4, range: [0.2, 0.8] }
        }
        keyframes: RunKeyframes
      }
    }
    
    // 四足行走
    quadrupedal: {
      walk: {
        name: '四足行走'
        requirements: { legs: 4, torso: 1 }
        parameters: {
          gait: 'walk' | 'trot' | 'pace' | 'bound'
          stepLength: { default: 0.8, range: [0.4, 1.5] }
          stepHeight: { default: 0.2, range: [0.1, 0.5] }
          frequency: { default: 1.2, range: [0.8, 2.5] }
          spineFlexion: { default: 0.1, range: [0.0, 0.3] }
        }
        keyframes: QuadWalkKeyframes
      }
      
      gallop: {
        name: '四足奔跑'
        requirements: { legs: 4, torso: 1 }
        parameters: {
          strideLength: { default: 2.0, range: [1.5, 4.0] }
          airTime: { default: 0.3, range: [0.1, 0.6] }
          frequency: { default: 2.5, range: [2.0, 4.0] }
          spineExtension: { default: 0.3, range: [0.1, 0.5] }
        }
        keyframes: GallopKeyframes
      }
    }
    
    // 蛇形移动
    serpentine: {
      slither: {
        name: '蛇形蠕动'
        requirements: { body_segments: { min: 5 } }
        parameters: {
          waveLength: { default: 3.0, range: [2.0, 6.0] }
          amplitude: { default: 0.5, range: [0.2, 1.0] }
          frequency: { default: 1.0, range: [0.5, 2.0] }
          phaseOffset: { default: 0.5, range: [0.0, 1.0] }
          verticalWave: { default: 0.1, range: [0.0, 0.3] }
        }
        keyframes: SlitherKeyframes
      }
    }
    
    // 飞行动画
    flying: {
      flap: {
        name: '翅膀拍打'
        requirements: { wings: { min: 2 } }
        parameters: {
          flapRate: { default: 2.0, range: [0.5, 8.0] }
          flapAmplitude: { default: 45, range: [20, 90] }  // 度数
          wingTwist: { default: 15, range: [0, 30] }
          bodyBob: { default: 0.1, range: [0.0, 0.3] }
          phaseOffset: { default: 0.0, range: [0.0, 0.5] }
        }
        keyframes: FlapKeyframes
      }
      
      glide: {
        name: '滑翔'
        requirements: { wings: { min: 2 } }
        parameters: {
          wingAngle: { default: 0, range: [-15, 15] }
          stabilization: { default: 0.1, range: [0.0, 0.3] }
          bodyTilt: { default: 5, range: [0, 20] }
        }
        keyframes: GlideKeyframes
      }
    }
  }
  
  // === 战斗动画模板 ===
  combat: {
    // 近战攻击
    melee: {
      slash: {
        name: '挥砍攻击'
        requirements: { arms: { min: 1 } }
        parameters: {
          windupTime: { default: 0.3, range: [0.1, 0.8] }
          strikeTime: { default: 0.2, range: [0.1, 0.5] }
          recoveryTime: { default: 0.5, range: [0.2, 1.0] }
          swingArc: { default: 90, range: [45, 180] }
          bodyRotation: { default: 30, range: [0, 60] }
        }
        keyframes: SlashKeyframes
      }
      
      thrust: {
        name: '刺击攻击'
        requirements: { arms: { min: 1 } }
        parameters: {
          windupTime: { default: 0.4, range: [0.2, 0.8] }
          thrustTime: { default: 0.15, range: [0.1, 0.3] }
          recoveryTime: { default: 0.45, range: [0.2, 0.8] }
          thrustDistance: { default: 1.0, range: [0.5, 2.0] }
          bodyLean: { default: 0.2, range: [0.0, 0.4] }
        }
        keyframes: ThrustKeyframes
      }
    }
    
    // 远程攻击
    ranged: {
      bow: {
        name: '弓箭射击'
        requirements: { arms: 2 }
        parameters: {
          drawTime: { default: 0.8, range: [0.5, 1.5] }
          aimTime: { default: 0.3, range: [0.1, 1.0] }
          releaseTime: { default: 0.1, range: [0.05, 0.2] }
          drawDistance: { default: 0.5, range: [0.3, 0.8] }
          bodyStance: { default: 0.2, range: [0.0, 0.4] }
        }
        keyframes: BowKeyframes
      }
      
      throw: {
        name: '投掷攻击'
        requirements: { arms: { min: 1 } }
        parameters: {
          windupTime: { default: 0.5, range: [0.3, 1.0] }
          throwTime: { default: 0.2, range: [0.1, 0.4] }
          followThrough: { default: 0.3, range: [0.1, 0.6] }
          throwArc: { default: 45, range: [20, 70] }
          bodyRotation: { default: 45, range: [20, 90] }
        }
        keyframes: ThrowKeyframes
      }
    }
  }

  // === 表情和情绪动画 ===
  emotional: {
    // 待机动画
    idle: {
      breathing: {
        name: '呼吸动画'
        requirements: { torso: 1 }
        parameters: {
          breathRate: { default: 0.3, range: [0.2, 0.8] }  // 呼吸频率
          chestExpansion: { default: 0.05, range: [0.02, 0.1] }
          shoulderMovement: { default: 0.02, range: [0.0, 0.05] }
          randomVariation: { default: 0.1, range: [0.0, 0.3] }
        }
        keyframes: BreathingKeyframes
      }

      fidget: {
        name: '小动作'
        requirements: { any: 1 }
        parameters: {
          frequency: { default: 0.1, range: [0.05, 0.3] }
          intensity: { default: 0.5, range: [0.2, 1.0] }
          randomness: { default: 0.8, range: [0.3, 1.0] }
          bodyParts: string[]  // 参与的身体部位
        }
        keyframes: FidgetKeyframes
      }
    }

    // 情绪表达
    emotions: {
      happy: {
        name: '快乐表情'
        requirements: { head: 1 }
        parameters: {
          intensity: { default: 0.7, range: [0.3, 1.0] }
          duration: { default: 2.0, range: [1.0, 5.0] }
          bodyLanguage: { default: 0.5, range: [0.0, 1.0] }
        }
        keyframes: HappyKeyframes
      }

      angry: {
        name: '愤怒表情'
        requirements: { head: 1 }
        parameters: {
          intensity: { default: 0.8, range: [0.4, 1.0] }
          duration: { default: 1.5, range: [0.8, 3.0] }
          bodyTension: { default: 0.6, range: [0.2, 1.0] }
        }
        keyframes: AngryKeyframes
      }
    }
  }
}
